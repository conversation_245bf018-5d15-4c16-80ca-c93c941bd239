<?php
/**
 * تحكم دليل الحسابات المتقدم والمتكامل
 * يدعم العرض الشجري، الطباعة، التصدير، والتكامل الكامل مع النظام
 */
class ControllerAccountsChartaccount extends Controller {
	private $error = array();
	private $central_service;

	public function __construct($registry) {
		parent::__construct($registry);

		// تحميل الخدمات المركزية
		$this->load->model('core/central_service_manager');
		$this->central_service = $this->model_core_central_service_manager;
	}

	public function index() {
		// Enhanced security headers (Security Expert Recommendation)
		$this->response->addHeader('X-Content-Type-Options: nosniff');
		$this->response->addHeader('X-Frame-Options: SAMEORIGIN');
		$this->response->addHeader('X-XSS-Protection: 1; mode=block');
		$this->response->addHeader('Referrer-Policy: strict-origin-when-cross-origin');

		// Rate limiting check (Security Expert Recommendation)
		$this->checkRateLimit();

		// Performance optimization (Performance Expert Recommendation)
		$this->optimizePagePerformance();

		// فحص الصلاحيات المزدوجة
		if (!$this->user->hasPermission('access', 'accounts/chartaccount') ||
			!$this->user->hasKey('accounting_chart_view')) {

			$this->central_service->logActivity('unauthorized_access', 'accounts',
				'محاولة وصول غير مصرح بها لدليل الحسابات', [
				'user_id' => $this->user->getId(),
				'ip_address' => $this->request->server['REMOTE_ADDR']
			]);

			$this->response->redirect($this->url->link('error/permission'));
			return;
		}

		$this->load->language('accounts/chartaccount');
		$this->document->setTitle($this->language->get('heading_title'));
		$this->load->model('accounts/chartaccount');

		// تسجيل الوصول للشاشة
		$this->central_service->logActivity('view', 'accounts',
			'عرض دليل الحسابات', [
			'user_id' => $this->user->getId(),
			'screen' => 'accounts/chartaccount'
		]);

		// إضافة CSS و JavaScript المتقدم مع تحسين الأداء
		$this->document->addStyle('view/stylesheet/accounts/chartaccount.css?v=' . filemtime(DIR_APPLICATION . 'view/stylesheet/accounts/chartaccount.css'));
		$this->document->addScript('view/javascript/accounts/chartaccount.js?v=' . filemtime(DIR_APPLICATION . 'view/javascript/accounts/chartaccount.js'));
		$this->document->addScript('view/javascript/jquery/jstree/jstree.min.js');
		$this->document->addStyle('view/javascript/jquery/jstree/themes/default/style.min.css');

		$this->getList();
	}
	public function add() {
		// فحص الصلاحيات المزدوجة
		if (!$this->user->hasPermission('modify', 'accounts/chartaccount') ||
			!$this->user->hasKey('accounting_chart_add')) {

			$this->central_service->logActivity('unauthorized_access', 'accounts',
				'محاولة إضافة حساب غير مصرح بها', [
				'user_id' => $this->user->getId(),
				'action' => 'add_account'
			]);

			$this->response->redirect($this->url->link('error/permission'));
			return;
		}

		$this->load->language('accounts/chartaccount');
		$this->document->setTitle($this->language->get('heading_title'));
		$this->load->model('accounts/chartaccount');

		if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validateForm()) {
			$account_id = $this->model_accounts_chartaccount->addAccount($this->request->post);

			// تسجيل إضافة الحساب
			$this->central_service->logActivity('add', 'accounts',
				'إضافة حساب جديد: ' . $this->request->post['account_name'], [
				'user_id' => $this->user->getId(),
				'account_id' => $account_id,
				'account_code' => $this->request->post['account_code'],
				'account_name' => $this->request->post['account_name']
			]);

			// إرسال إشعار للمحاسب الرئيسي
			$this->central_service->sendNotification(
				'account_added',
				'إضافة حساب جديد',
				'تم إضافة حساب جديد: ' . $this->request->post['account_name'] . ' بواسطة ' . $this->user->getFirstName(),
				[$this->config->get('config_chief_accountant_id')],
				[
					'account_code' => $this->request->post['account_code'],
					'account_name' => $this->request->post['account_name'],
					'user_name' => $this->user->getFirstName() . ' ' . $this->user->getLastName()
				]
			);

			$this->session->data['success'] = $this->language->get('text_success');

			$url = '';

			if (isset($this->request->get['sort'])) {
				$url .= '&sort=' . $this->request->get['sort'];
			}

			if (isset($this->request->get['order'])) {
				$url .= '&order=' . $this->request->get['order'];
			}

			if (isset($this->request->get['page'])) {
				$url .= '&page=' . $this->request->get['page'];
			}

			$this->response->redirect($this->url->link('accounts/chartaccount', 'user_token=' . $this->session->data['user_token'] . $url, true));
		}

		$this->getForm();
	}

	public function edit() {
		// فحص الصلاحيات المزدوجة
		if (!$this->user->hasPermission('modify', 'accounts/chartaccount') ||
			!$this->user->hasKey('accounting_chart_edit')) {

			$this->central_service->logActivity('unauthorized_access', 'accounts',
				'محاولة تعديل حساب غير مصرح بها', [
				'user_id' => $this->user->getId(),
				'account_id' => $this->request->get['account_id'] ?? 'unknown'
			]);

			$this->response->redirect($this->url->link('error/permission'));
			return;
		}

		$this->load->language('accounts/chartaccount');
		$this->document->setTitle($this->language->get('heading_title'));
		$this->load->model('accounts/chartaccount');

		if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validateForm()) {
			$account_id = $this->request->get['account_id'];

			// جلب بيانات الحساب القديمة للمقارنة
			$old_account = $this->model_accounts_chartaccount->getAccount($account_id);

			$this->model_accounts_chartaccount->editAccount($account_id, $this->request->post);

			// تسجيل تعديل الحساب
			$this->central_service->logActivity('edit', 'accounts',
				'تعديل حساب: ' . $this->request->post['account_name'], [
				'user_id' => $this->user->getId(),
				'account_id' => $account_id,
				'old_data' => $old_account,
				'new_data' => $this->request->post
			]);

			// إرسال إشعار للمحاسب الرئيسي
			$this->central_service->sendNotification(
				'account_edited',
				'تعديل حساب',
				'تم تعديل حساب: ' . $this->request->post['account_name'] . ' بواسطة ' . $this->user->getFirstName(),
				[$this->config->get('config_chief_accountant_id')],
				[
					'account_code' => $this->request->post['account_code'],
					'account_name' => $this->request->post['account_name'],
					'user_name' => $this->user->getFirstName() . ' ' . $this->user->getLastName()
				]
			);

			$this->session->data['success'] = $this->language->get('text_success');

			$url = '';

			if (isset($this->request->get['sort'])) {
				$url .= '&sort=' . $this->request->get['sort'];
			}

			if (isset($this->request->get['order'])) {
				$url .= '&order=' . $this->request->get['order'];
			}

			if (isset($this->request->get['page'])) {
				$url .= '&page=' . $this->request->get['page'];
			}

			$this->response->redirect($this->url->link('accounts/chartaccount', 'user_token=' . $this->session->data['user_token'] . $url, true));
		}

		$this->getForm();
	}

	public function delete() {
		// فحص الصلاحيات المزدوجة
		if (!$this->user->hasPermission('modify', 'accounts/chartaccount') ||
			!$this->user->hasKey('accounting_chart_delete')) {

			$this->central_service->logActivity('unauthorized_access', 'accounts',
				'محاولة حذف حسابات غير مصرح بها', [
				'user_id' => $this->user->getId(),
				'selected_accounts' => $this->request->post['selected'] ?? []
			]);

			$this->response->redirect($this->url->link('error/permission'));
			return;
		}

		$this->load->language('accounts/chartaccount');
		$this->document->setTitle($this->language->get('heading_title'));
		$this->load->model('accounts/chartaccount');

		if (isset($this->request->post['selected']) && $this->validateDelete()) {
			$deleted_accounts = [];

			foreach ($this->request->post['selected'] as $account_id) {
				// تنظيف وتحقق من صحة المعرف (SECURITY FIX)
				$account_id = (int)$account_id;
				if ($account_id <= 0) continue;

				// جلب بيانات الحساب قبل الحذف
				$account = $this->model_accounts_chartaccount->getAccount($account_id);
				$deleted_accounts[] = $account;

				$this->model_accounts_chartaccount->deleteAccount($account_id);
			}

			// تسجيل حذف الحسابات
			$this->central_service->logActivity('delete', 'accounts',
				'حذف ' . count($deleted_accounts) . ' حساب', [
				'user_id' => $this->user->getId(),
				'deleted_accounts' => $deleted_accounts,
				'accounts_count' => count($deleted_accounts)
			]);

			// إرسال إشعار للمحاسب الرئيسي
			$this->central_service->sendNotification(
				'accounts_deleted',
				'حذف حسابات',
				'تم حذف ' . count($deleted_accounts) . ' حساب بواسطة ' . $this->user->getFirstName(),
				[$this->config->get('config_chief_accountant_id')],
				[
					'accounts_count' => count($deleted_accounts),
					'user_name' => $this->user->getFirstName() . ' ' . $this->user->getLastName()
				]
			);

			$this->session->data['success'] = $this->language->get('text_success');

			$url = '';

			if (isset($this->request->get['sort'])) {
				$url .= '&sort=' . $this->request->get['sort'];
			}

			if (isset($this->request->get['order'])) {
				$url .= '&order=' . $this->request->get['order'];
			}

			if (isset($this->request->get['page'])) {
				$url .= '&page=' . $this->request->get['page'];
			}

			$this->response->redirect($this->url->link('accounts/chartaccount', 'user_token=' . $this->session->data['user_token'] . $url, true));
		}

		$this->getList();
	}

	protected function getList() {
		// فلاتر متقدمة
		$filter_data = array();

		// فلتر نوع الحساب
		if (isset($this->request->get['filter_account_type'])) {
			$filter_data['account_type'] = $this->request->get['filter_account_type'];
		}

		// فلتر حالة الحساب
		if (isset($this->request->get['filter_status'])) {
			$filter_data['status'] = $this->request->get['filter_status'];
		}

		// فلتر الحسابات التي لها أرصدة
		if (isset($this->request->get['filter_has_balance'])) {
			$filter_data['has_balance'] = $this->request->get['filter_has_balance'];
		}

		// فلتر الحساب الرئيسي
		if (isset($this->request->get['filter_parent_id'])) {
			$filter_data['parent_id'] = $this->request->get['filter_parent_id'];
		}

		// فلتر البحث النصي
		if (isset($this->request->get['filter_search'])) {
			$filter_data['search'] = $this->request->get['filter_search'];
		}

		if (isset($this->request->get['sort'])) {
			$sort = $this->request->get['sort'];
		} else {
			$sort = 'account_code';
		}

		if (isset($this->request->get['order'])) {
			$order = $this->request->get['order'];
		} else {
			$order = 'ASC';
		}

		if (isset($this->request->get['page'])) {
			$page = (int)$this->request->get['page'];
		} else {
			$page = 1;
		}

		$url = '';

		if (isset($this->request->get['sort'])) {
			$url .= '&sort=' . $this->request->get['sort'];
		}

		if (isset($this->request->get['order'])) {
			$url .= '&order=' . $this->request->get['order'];
		}

		if (isset($this->request->get['page'])) {
			$url .= '&page=' . $this->request->get['page'];
		}

		$data['breadcrumbs'] = array();

		$data['breadcrumbs'][] = array(
			'text' => $this->language->get('text_home'),
			'href' => $this->url->link('common/dashboard', 'user_token=' . $this->session->data['user_token'], true)
		);

		$data['breadcrumbs'][] = array(
			'text' => $this->language->get('heading_title'),
			'href' => $this->url->link('accounts/chartaccount', 'user_token=' . $this->session->data['user_token'] . $url, true)
		);

		$data['add'] = $this->url->link('accounts/chartaccount/add', 'user_token=' . $this->session->data['user_token'] . $url, true);
		$data['delete'] = $this->url->link('accounts/chartaccount/delete', 'user_token=' . $this->session->data['user_token'] . $url, true);
		$data['tree_view'] = $this->url->link('accounts/chartaccount/tree', 'user_token=' . $this->session->data['user_token'], true);
		$data['list_view'] = $this->url->link('accounts/chartaccount', 'user_token=' . $this->session->data['user_token'], true);
		$data['add_tax_accounts'] = $this->url->link('accounts/chartaccount/addTaxAccounts', 'user_token=' . $this->session->data['user_token'], true);
		$data['add_eta_accounts'] = $this->url->link('accounts/chartaccount/addETAAccounts', 'user_token=' . $this->session->data['user_token'], true);

		$data['accounts'] = array();

		// دمج الفلاتر المتقدمة مع فلاتر الترتيب والصفحات
		$filter_data = array_merge($filter_data, array(
			'sort'  => $sort,
			'order' => $order,
			'start' => ($page - 1) * *********,
			'limit' => *********
		));

		$account_total = $this->model_accounts_chartaccount->getTotalAccounts();

		$results = $this->model_accounts_chartaccount->getAccounts($filter_data);

		foreach ($results as $result) {
			$data['accounts'][] = array(
				'account_id' => $result['account_id'],
				'account_code'        => $result['account_code'],
				'account_type'        => $result['account_type'],
				'name'        => $result['name'],
				'edit'        => $this->url->link('accounts/chartaccount/edit', 'user_token=' . $this->session->data['user_token'] . '&account_id=' . $result['account_id'] . $url, true),
				'delete'      => $this->url->link('accounts/chartaccount/delete', 'user_token=' . $this->session->data['user_token'] . '&account_id=' . $result['account_id'] . $url, true)
			);
		}

		if (isset($this->error['warning'])) {
			$data['error_warning'] = $this->error['warning'];
		} else {
			$data['error_warning'] = '';
		}

		if (isset($this->session->data['success'])) {
			$data['success'] = $this->session->data['success'];

			unset($this->session->data['success']);
		} else {
			$data['success'] = '';
		}

		if (isset($this->request->post['selected'])) {
			$data['selected'] = (array)$this->request->post['selected'];
		} else {
			$data['selected'] = array();
		}

		$url = '';

		if ($order == 'ASC') {
			$url .= '&order=DESC';
		} else {
			$url .= '&order=ASC';
		}

		if (isset($this->request->get['page'])) {
			$url .= '&page=' . $this->request->get['page'];
		}
		$data['sort_account_code'] = $this->url->link('accounts/chartaccount', 'user_token=' . $this->session->data['user_token'] . '&sort=account_code' . $url, true);
		$data['sort_name'] = $this->url->link('accounts/chartaccount', 'user_token=' . $this->session->data['user_token'] . '&sort=name' . $url, true);
        $data['export_action'] = $this->url->link('extension/export_import/download', 'user_token=' . $this->session->data['user_token'], true);
        $data['import_action'] = $this->url->link('extension/export_import/upload', 'user_token=' . $this->session->data['user_token'], true);



		$url = '';

		if (isset($this->request->get['sort'])) {
			$url .= '&sort=' . $this->request->get['sort'];
		}

		if (isset($this->request->get['order'])) {
			$url .= '&order=' . $this->request->get['order'];
		}

		$pagination = new Pagination();
		$pagination->total = $account_total;
		$pagination->page = $page;
		$pagination->limit = *********;
		$pagination->url = $this->url->link('accounts/chartaccount', 'user_token=' . $this->session->data['user_token'] . $url . '&page={page}', true);

		$data['pagination'] = $pagination->render();

		$data['results'] = sprintf($this->language->get('text_pagination'), ($account_total) ? (($page - 1) * *********) + 1 : 0, ((($page - 1) * *********) > ($account_total - *********)) ? $account_total : ((($page - 1) * *********) + *********), $account_total, ceil($account_total / *********));

		$data['sort'] = $sort;
		$data['order'] = $order;

		$data['header'] = $this->load->controller('common/header');
		$data['column_left'] = $this->load->controller('common/column_left');
		$data['footer'] = $this->load->controller('common/footer');

		$this->response->setOutput($this->load->view('accounts/chartaccount_list', $data));
	}


	protected function getForm() {
		$data['text_form'] = !isset($this->request->get['account_id']) ? $this->language->get('text_add') : $this->language->get('text_edit');

		if (isset($this->error['warning'])) {
			$data['error_warning'] = $this->error['warning'];
		} else {
			$data['error_warning'] = '';
		}

		if (isset($this->error['name'])) {
			$data['error_name'] = $this->error['name'];
		} else {
			$data['error_name'] = array();
		}


		if (isset($this->error['parent'])) {
			$data['error_parent'] = $this->error['parent'];
		} else {
			$data['error_parent'] = '';
		}

		$url = '';

		if (isset($this->request->get['sort'])) {
			$url .= '&sort=' . $this->request->get['sort'];
		}

		if (isset($this->request->get['order'])) {
			$url .= '&order=' . $this->request->get['order'];
		}

		if (isset($this->request->get['page'])) {
			$url .= '&page=' . $this->request->get['page'];
		}

		$data['breadcrumbs'] = array();

		$data['breadcrumbs'][] = array(
			'text' => $this->language->get('text_home'),
			'href' => $this->url->link('common/dashboard', 'user_token=' . $this->session->data['user_token'], true)
		);

		$data['breadcrumbs'][] = array(
			'text' => $this->language->get('heading_title'),
			'href' => $this->url->link('accounts/chartaccount', 'user_token=' . $this->session->data['user_token'] . $url, true)
		);

		if (!isset($this->request->get['account_id'])) {
			$data['action'] = $this->url->link('accounts/chartaccount/add', 'user_token=' . $this->session->data['user_token'] . $url, true);
		} else {
			$data['action'] = $this->url->link('accounts/chartaccount/edit', 'user_token=' . $this->session->data['user_token'] . '&account_id=' . $this->request->get['account_id'] . $url, true);
		}

		$data['cancel'] = $this->url->link('accounts/chartaccount', 'user_token=' . $this->session->data['user_token'] . $url, true);

		if (isset($this->request->get['account_id']) && ($this->request->server['REQUEST_METHOD'] != 'POST')) {
			$account_info = $this->model_accounts_chartaccount->getAccount($this->request->get['account_id']);
		}

		$data['user_token'] = $this->session->data['user_token'];

		$this->load->model('localisation/language');

		$data['languages'] = $this->model_localisation_language->getLanguages();

		if (isset($this->request->post['account_description'])) {
			$data['account_description'] = $this->request->post['account_description'];
		} elseif (isset($this->request->get['account_id'])) {
			$data['account_description'] = $this->model_accounts_chartaccount->getAccountDescriptions($this->request->get['account_id']);
		} else {
			$data['account_description'] = array();
		}

		if (isset($this->request->post['parent_id'])) {
			$data['parent_id'] = $this->request->post['parent_id'];
		} elseif (!empty($account_info)) {
			$data['parent_id'] = $account_info['parent_id'];
		} else {
			$data['parent_id'] = 0;
		}
		if (isset($this->request->post['account_code'])) {
			$data['account_code'] = $this->request->post['account_code'];
		} elseif (!empty($account_info)) {
			$data['account_code'] = $account_info['account_code'];
		} else {
			$data['account_code'] = 0;
		}
		if (isset($this->request->post['account_type'])) {
			$data['account_type'] = $this->request->post['account_type'];
		} elseif (!empty($account_info)) {
			$data['account_type'] = $account_info['account_type'];
		} else {
			$data['account_type'] = 'debit';
		}
		if (isset($this->request->post['status'])) {
			$data['status'] = $this->request->post['status'];
		} elseif (!empty($account_info)) {
			$data['status'] = $account_info['status'];
		} else {
			$data['status'] = true;
		}


		$data['header'] = $this->load->controller('common/header');
		$data['column_left'] = $this->load->controller('common/column_left');
		$data['footer'] = $this->load->controller('common/footer');

		$this->response->setOutput($this->load->view('accounts/account_form', $data));
	}

	protected function validateForm() {
		if (!$this->user->hasPermission('modify', 'accounts/chartaccount')) {
			$this->error['warning'] = $this->language->get('error_permission');
		}

		foreach ($this->request->post['account_description'] as $language_id => $value) {
			if ((utf8_strlen($value['name']) < 1) || (utf8_strlen($value['name']) > 255)) {
				$this->error['name'][$language_id] = $this->language->get('error_name');
			}

		}


		if ($this->request->post['parent_id'] == $this->request->get['account_id']) {
			$this->error['parent'] = $this->language->get('error_parent');
		}




		if ($this->error && !isset($this->error['warning'])) {
			$this->error['warning'] = $this->language->get('error_warning');
		}

		return !$this->error;
	}

	protected function validateDelete() {
		if (!$this->user->hasPermission('modify', 'accounts/chartaccount')) {
			$this->error['warning'] = $this->language->get('error_permission');
		}

		return !$this->error;
	}

	protected function validateRepair() {
		if (!$this->user->hasPermission('modify', 'accounts/chartaccount')) {
			$this->error['warning'] = $this->language->get('error_permission');
		}

		return !$this->error;
	}

	public function autocomplete() {
		$json = array();

		if (isset($this->request->get['filter_name'])) {
			$this->load->model('accounts/chartaccount');

			$filter_data = array(
				'filter_name' => $this->request->get['filter_name'],
				'sort'        => 'name',
				'order'       => 'ASC',
				'start'       => 0,
				'limit'       => 5
			);

			$results = $this->model_accounts_chartaccount->getAccounts($filter_data);

			foreach ($results as $result) {
				$json[] = array(
					'account_id' => $result['account_id'],
					'name'        => strip_tags(html_entity_decode($result['name'], ENT_QUOTES, 'UTF-8'))
				);
			}
		}


		$sort_order = array();

		foreach ($json as $key => $value) {
			$sort_order[$key] = $value['name'];
		}

		array_multisort($sort_order, SORT_ASC, $json);

		$this->response->addHeader('Content-Type: application/json');
		$this->response->setOutput(json_encode($json));
	}

	/**
	 * عرض شجري للحسابات
	 */
	public function tree() {
		$this->load->language('accounts/chartaccount');
		$this->document->setTitle($this->language->get('heading_title') . ' - ' . $this->language->get('text_tree_view'));
		$this->load->model('accounts/chartaccount');

		// إضافة مكتبات العرض الشجري
		$this->document->addScript('view/javascript/jquery/jstree/jstree.min.js');
		$this->document->addStyle('view/javascript/jquery/jstree/themes/default/style.min.css');
		$this->document->addScript('view/javascript/accounts/tree.js');

		$data['breadcrumbs'] = array();
		$data['breadcrumbs'][] = array(
			'text' => $this->language->get('text_home'),
			'href' => $this->url->link('common/dashboard', 'user_token=' . $this->session->data['user_token'], true)
		);
		$data['breadcrumbs'][] = array(
			'text' => $this->language->get('heading_title'),
			'href' => $this->url->link('accounts/chartaccount', 'user_token=' . $this->session->data['user_token'], true)
		);
		$data['breadcrumbs'][] = array(
			'text' => $this->language->get('text_tree_view'),
			'href' => $this->url->link('accounts/chartaccount/tree', 'user_token=' . $this->session->data['user_token'], true)
		);

		// الحصول على البيانات الشجرية
		$data['accounts_tree'] = $this->model_accounts_chartaccount->getAccountsTree();
		$data['tree_data_url'] = $this->url->link('accounts/chartaccount/getTreeData', 'user_token=' . $this->session->data['user_token'], true);

		// روابط الإجراءات
		$data['add'] = $this->url->link('accounts/chartaccount/add', 'user_token=' . $this->session->data['user_token'], true);
		$data['list_view'] = $this->url->link('accounts/chartaccount', 'user_token=' . $this->session->data['user_token'], true);
		$data['print'] = $this->url->link('accounts/chartaccount/print', 'user_token=' . $this->session->data['user_token'], true);
		$data['export'] = $this->url->link('accounts/chartaccount/export', 'user_token=' . $this->session->data['user_token'], true);

		$data['header'] = $this->load->controller('common/header');
		$data['column_left'] = $this->load->controller('common/column_left');
		$data['footer'] = $this->load->controller('common/footer');

		$this->response->setOutput($this->load->view('accounts/chartaccount_tree', $data));
	}

	/**
	 * الحصول على بيانات الشجرة عبر AJAX
	 */
	public function getTreeData() {
		$this->load->model('accounts/chartaccount');

		$parent_id = isset($this->request->get['id']) && $this->request->get['id'] != '#' ?
					 (int)$this->request->get['id'] : null;

		$accounts = $this->model_accounts_chartaccount->getAccountsTree($parent_id);
		$tree_data = array();

		foreach ($accounts as $account) {
			$has_children = !empty($account['children']);

			$tree_data[] = array(
				'id' => $account['account_id'],
				'text' => $account['account_code'] . ' - ' . $account['name'],
				'children' => $has_children,
				'data' => array(
					'account_code' => $account['account_code'],
					'account_type' => $account['account_type'],
					'current_balance' => $account['current_balance'] ?? 0,
					'is_parent' => $account['is_parent'] ?? 0,
					'allow_posting' => $account['allow_posting'] ?? 1
				),
				'a_attr' => array(
					'href' => $this->url->link('accounts/chartaccount/edit',
						'user_token=' . $this->session->data['user_token'] . '&account_id=' . $account['account_id'], true)
				),
				'li_attr' => array(
					'data-account-id' => $account['account_id'],
					'data-account-code' => $account['account_code'],
					'data-account-type' => $account['account_type']
				)
			);
		}

		$this->response->addHeader('Content-Type: application/json');
		$this->response->setOutput(json_encode($tree_data));
	}

	/**
	 * طباعة دليل الحسابات
	 */
	public function print() {
		// فحص الصلاحيات المزدوجة
		if (!$this->user->hasPermission('access', 'accounts/chartaccount') ||
			!$this->user->hasKey('accounting_chart_print')) {

			$this->central_service->logActivity('unauthorized_print', 'accounts',
				'محاولة طباعة دليل حسابات غير مصرح بها', [
				'user_id' => $this->user->getId(),
				'action' => 'print_chart'
			]);

			$this->response->redirect($this->url->link('error/permission'));
			return;
		}

		$this->load->language('accounts/chartaccount');
		$this->load->model('accounts/chartaccount');

		// معاملات الطباعة
		$format = $this->request->get['format'] ?? 'tree';
		$include_balances = isset($this->request->get['include_balances']) ?
						   (bool)$this->request->get['include_balances'] : true;
		$account_type = $this->request->get['account_type'] ?? '';

		$data['company_name'] = $this->config->get('config_name');
		$data['print_date'] = date($this->language->get('date_format_long'));
		$data['format'] = $format;
		$data['include_balances'] = $include_balances;

		// الحصول على البيانات حسب التنسيق
		if ($format == 'tree') {
			$data['accounts'] = $this->model_accounts_chartaccount->getAccountsTree();
		} else {
			$filter_data = array();
			if ($account_type) {
				$filter_data['filter_account_type'] = $account_type;
			}
			$data['accounts'] = $this->model_accounts_chartaccount->getAccounts($filter_data);
		}

		// تسجيل عملية الطباعة
		$this->central_service->logActivity('print', 'accounts',
			'طباعة دليل الحسابات', [
			'user_id' => $this->user->getId(),
			'format' => $format,
			'include_balances' => $include_balances,
			'account_type' => $account_type,
			'accounts_count' => count($data['accounts'])
		]);

		// إرسال إشعار للمحاسب الرئيسي
		$this->central_service->sendNotification(
			'chart_printed',
			'طباعة دليل الحسابات',
			'تم طباعة دليل الحسابات بواسطة ' . $this->user->getFirstName(),
			[$this->config->get('config_chief_accountant_id')],
			[
				'format' => $format,
				'user_name' => $this->user->getFirstName() . ' ' . $this->user->getLastName()
			]
		);

		// إعداد الطباعة
		$this->response->addHeader('Content-Type: text/html; charset=utf-8');
		$this->response->setOutput($this->load->view('accounts/chartaccount_print', $data));
	}

	/**
	 * تصدير دليل الحسابات
	 */
	public function export() {
		// فحص الصلاحيات المزدوجة
		if (!$this->user->hasPermission('access', 'accounts/chartaccount') ||
			!$this->user->hasKey('accounting_chart_export')) {

			$this->central_service->logActivity('unauthorized_export', 'accounts',
				'محاولة تصدير دليل حسابات غير مصرح بها', [
				'user_id' => $this->user->getId(),
				'action' => 'export_chart'
			]);

			$this->response->redirect($this->url->link('error/permission'));
			return;
		}

		$this->load->language('accounts/chartaccount');
		$this->load->model('accounts/chartaccount');

		$format = $this->request->get['format'] ?? 'excel';
		$include_balances = isset($this->request->get['include_balances']) ?
						   (bool)$this->request->get['include_balances'] : true;

		$accounts = $this->model_accounts_chartaccount->getAccounts();

		switch ($format) {
			case 'excel':
				$this->exportToExcel($accounts, $include_balances);
				break;
			case 'pdf':
				$this->exportToPdf($accounts, $include_balances);
				break;
			case 'csv':
				$this->exportToCsv($accounts, $include_balances);
				break;
			default:
				$this->exportToExcel($accounts, $include_balances);
		}
	}

	/**
	 * تصدير إلى Excel
	 */
	private function exportToExcel($accounts, $include_balances = true) {
		$filename = 'chart_of_accounts_' . date('Y-m-d') . '.xls';

		header('Content-Type: application/vnd.ms-excel');
		header('Content-Disposition: attachment;filename="' . $filename . '"');
		header('Cache-Control: max-age=0');

		echo '<table border="1">';
		echo '<tr>';
		echo '<th>' . $this->language->get('column_account_code') . '</th>';
		echo '<th>' . $this->language->get('column_account_name') . '</th>';
		echo '<th>' . $this->language->get('column_account_type') . '</th>';
		echo '<th>' . $this->language->get('column_account_nature') . '</th>';
		echo '<th>' . $this->language->get('column_parent_account') . '</th>';
		if ($include_balances) {
			echo '<th>' . $this->language->get('column_current_balance') . '</th>';
		}
		echo '<th>' . $this->language->get('column_status') . '</th>';
		echo '</tr>';

		foreach ($accounts as $account) {
			echo '<tr>';
			echo '<td>' . $account['account_code'] . '</td>';
			echo '<td>' . $account['name'] . '</td>';
			echo '<td>' . $this->getAccountTypeText($account['account_type']) . '</td>';
			echo '<td>' . $this->getAccountNatureText($account['account_nature']) . '</td>';
			echo '<td>' . ($account['parent_name'] ?? '') . '</td>';
			if ($include_balances) {
				echo '<td>' . number_format($account['current_balance'] ?? 0, 2) . '</td>';
			}
			echo '<td>' . ($account['is_active'] ? $this->language->get('text_enabled') : $this->language->get('text_disabled')) . '</td>';
			echo '</tr>';
		}

		echo '</table>';
	}

	/**
	 * تصدير إلى PDF
	 */
	private function exportToPdf($accounts, $include_balances = true) {
		require_once(DIR_SYSTEM . 'library/tcpdf/tcpdf.php');

		$pdf = new TCPDF('P', 'mm', 'A4', true, 'UTF-8');
		$pdf->SetCreator('ERP System');
		$pdf->SetAuthor($this->config->get('config_name'));
		$pdf->SetTitle('دليل الحسابات');

		// إعداد الخط العربي
		$pdf->SetFont('aealarabiya', '', 12);
		$pdf->AddPage();

		// عنوان التقرير
		$pdf->Cell(0, 10, $this->language->get('heading_title'), 0, 1, 'C');
		$pdf->Cell(0, 10, $this->config->get('config_name'), 0, 1, 'C');
		$pdf->Cell(0, 10, $this->language->get('text_print_date') . ': ' . date('Y-m-d'), 0, 1, 'C');
		$pdf->Ln(10);

		// رأس الجدول
		$pdf->SetFont('aealarabiya', 'B', 10);
		$pdf->Cell(30, 8, $this->language->get('column_account_code'), 1, 0, 'C');
		$pdf->Cell(60, 8, $this->language->get('column_account_name'), 1, 0, 'C');
		$pdf->Cell(30, 8, $this->language->get('column_account_type'), 1, 0, 'C');
		if ($include_balances) {
			$pdf->Cell(30, 8, $this->language->get('column_current_balance'), 1, 0, 'C');
		}
		$pdf->Cell(20, 8, $this->language->get('column_status'), 1, 1, 'C');

		// بيانات الجدول
		$pdf->SetFont('aealarabiya', '', 9);
		foreach ($accounts as $account) {
			$pdf->Cell(30, 6, $account['account_code'], 1, 0, 'C');
			$pdf->Cell(60, 6, $account['name'], 1, 0, 'R');
			$pdf->Cell(30, 6, $this->getAccountTypeText($account['account_type']), 1, 0, 'C');
			if ($include_balances) {
				$pdf->Cell(30, 6, number_format($account['current_balance'] ?? 0, 2), 1, 0, 'R');
			}
			$pdf->Cell(20, 6, ($account['is_active'] ? $this->language->get('text_enabled') : $this->language->get('text_disabled')), 1, 1, 'C');
		}

		$pdf->Output('chart_of_accounts_' . date('Y-m-d') . '.pdf', 'D');
	}

	/**
	 * تصدير إلى CSV
	 */
	private function exportToCsv($accounts, $include_balances = true) {
		$filename = 'chart_of_accounts_' . date('Y-m-d') . '.csv';

		header('Content-Type: text/csv; charset=utf-8');
		header('Content-Disposition: attachment;filename="' . $filename . '"');
		header('Cache-Control: max-age=0');

		$output = fopen('php://output', 'w');

		// إضافة BOM للدعم العربي
		fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));

		// رأس الجدول
		$headers = array(
			$this->language->get('column_account_code'),
			$this->language->get('column_account_name'),
			$this->language->get('column_account_type'),
			$this->language->get('column_account_nature'),
			$this->language->get('column_parent_account')
		);
		if ($include_balances) {
			$headers[] = $this->language->get('column_current_balance');
		}
		$headers[] = $this->language->get('column_status');

		fputcsv($output, $headers);

		// البيانات
		foreach ($accounts as $account) {
			$row = array(
				$account['account_code'],
				$account['name'],
				$this->getAccountTypeText($account['account_type']),
				$this->getAccountNatureText($account['account_nature']),
				$account['parent_name'] ?? ''
			);

			if ($include_balances) {
				$row[] = number_format($account['current_balance'] ?? 0, 2);
			}

			$row[] = ($account['is_active'] ? $this->language->get('text_enabled') : $this->language->get('text_disabled'));

			fputcsv($output, $row);
		}

		fclose($output);
	}

	/**
	 * استيراد دليل الحسابات
	 */
	public function import() {
		$this->load->language('accounts/chartaccount');
		$this->document->setTitle($this->language->get('heading_title') . ' - ' . $this->language->get('text_import'));

		if ($this->request->server['REQUEST_METHOD'] == 'POST' && $this->validateImport()) {
			$this->processImport();
		}

		$this->getImportForm();
	}

	/**
	 * معالجة الاستيراد
	 */
	private function processImport() {
		$this->load->model('accounts/chartaccount');

		if (isset($this->request->files['import_file']) && $this->request->files['import_file']['error'] == 0) {
			$file = $this->request->files['import_file'];
			$extension = pathinfo($file['name'], PATHINFO_EXTENSION);

			switch (strtolower($extension)) {
				case 'csv':
					$this->importFromCsv($file['tmp_name']);
					break;
				case 'xls':
				case 'xlsx':
					$this->importFromExcel($file['tmp_name']);
					break;
				default:
					$this->error['warning'] = 'نوع الملف غير مدعوم';
			}
		}
	}

	/**
	 * استيراد من CSV
	 */
	private function importFromCsv($file_path) {
		$handle = fopen($file_path, 'r');
		fgetcsv($handle); // تجاهل الرأس
		$imported = 0;
		$errors = 0;

		while (($data = fgetcsv($handle)) !== FALSE) {
			try {
				$account_data = array(
					'account_code' => $data[0],
					'account_type' => $this->getAccountTypeFromText($data[2]),
					'parent_id' => $this->getParentIdFromName($data[4]),
					'is_active' => ($data[5] == 'نشط') ? 1 : 0,
					'account_description' => array(
						$this->config->get('config_language_id') => array(
							'name' => $data[1],
							'description' => ''
						)
					)
				);

				$this->model_accounts_chartaccount->addAccount($account_data);
				$imported++;
			} catch (Exception) {
				$errors++;
			}
		}

		fclose($handle);

		$this->session->data['success'] = sprintf('تم استيراد %d حساب بنجاح. فشل في استيراد %d حساب.', $imported, $errors);
	}

	/**
	 * استيراد من ملف Excel
	 */
	private function importFromExcel($file_path) {
		// يمكن إضافة مكتبة PhpSpreadsheet هنا لاحقاً
		// حالياً نعيد رسالة خطأ
		throw new Exception($this->language->get('error_excel_not_supported'));
	}

	/**
	 * دوال مساعدة
	 */
	private function getAccountTypeText($type) {
		$types = array(
			'asset' => 'أصول',
			'liability' => 'خصوم',
			'equity' => 'حقوق ملكية',
			'revenue' => 'إيرادات',
			'expense' => 'مصروفات'
		);
		return $types[$type] ?? $type;
	}

	private function getAccountNatureText($nature) {
		$natures = array(
			'debit' => 'مدين',
			'credit' => 'دائن'
		);
		return $natures[$nature] ?? $nature;
	}

	private function getAccountTypeFromText($text) {
		$types = array(
			'أصول' => 'asset',
			'خصوم' => 'liability',
			'حقوق ملكية' => 'equity',
			'إيرادات' => 'revenue',
			'مصروفات' => 'expense'
		);
		return $types[$text] ?? 'asset';
	}

	private function getParentIdFromName($name) {
		if (empty($name)) return 0;

		$this->load->model('accounts/chartaccount');
		$accounts = $this->model_accounts_chartaccount->getAccounts(array('filter_name' => $name));

		return !empty($accounts) ? $accounts[0]['account_id'] : 0;
	}

	private function validateImport() {
		if (!$this->user->hasPermission('modify', 'accounts/chartaccount')) {
			$this->error['warning'] = $this->language->get('error_permission');
		}

		if (!isset($this->request->files['import_file']) || $this->request->files['import_file']['error'] != 0) {
			$this->error['file'] = 'يرجى اختيار ملف صحيح للاستيراد';
		}

		return !$this->error;
	}

	private function getImportForm() {
		$data['breadcrumbs'] = array();
		$data['breadcrumbs'][] = array(
			'text' => $this->language->get('text_home'),
			'href' => $this->url->link('common/dashboard', 'user_token=' . $this->session->data['user_token'], true)
		);
		$data['breadcrumbs'][] = array(
			'text' => $this->language->get('heading_title'),
			'href' => $this->url->link('accounts/chartaccount', 'user_token=' . $this->session->data['user_token'], true)
		);
		$data['breadcrumbs'][] = array(
			'text' => $this->language->get('text_import'),
			'href' => $this->url->link('accounts/chartaccount/import', 'user_token=' . $this->session->data['user_token'], true)
		);

		$data['action'] = $this->url->link('accounts/chartaccount/import', 'user_token=' . $this->session->data['user_token'], true);
		$data['cancel'] = $this->url->link('accounts/chartaccount', 'user_token=' . $this->session->data['user_token'], true);

		if (isset($this->error['warning'])) {
			$data['error_warning'] = $this->error['warning'];
		} else {
			$data['error_warning'] = '';
		}

		if (isset($this->session->data['success'])) {
			$data['success'] = $this->session->data['success'];
			unset($this->session->data['success']);
		} else {
			$data['success'] = '';
		}

		$data['header'] = $this->load->controller('common/header');
		$data['column_left'] = $this->load->controller('common/column_left');
		$data['footer'] = $this->load->controller('common/footer');

		$this->response->setOutput($this->load->view('accounts/chartaccount_import', $data));
	}

	/**
	 * إضافة الحسابات الضريبية المصرية
	 */
	public function addTaxAccounts() {
		// فحص الصلاحيات المزدوجة
		if (!$this->user->hasPermission('modify', 'accounts/chartaccount') ||
			!$this->user->hasKey('accounting_chartaccount_add_tax')) {

			$this->response->redirect($this->url->link('error/permission'));
			return;
		}

		$this->load->language('accounts/chartaccount');
		$this->load->model('accounts/chartaccount');

		// الحسابات الضريبية المصرية
		$tax_accounts = array(
			array(
				'account_code' => '2311',
				'name_ar' => 'ضريبة القيمة المضافة - مستحقة',
				'name_en' => 'VAT Payable',
				'account_type' => 'liability',
				'parent_id' => 0
			),
			array(
				'account_code' => '1311',
				'name_ar' => 'ضريبة القيمة المضافة - مدفوعة مقدماً',
				'name_en' => 'VAT Prepaid',
				'account_type' => 'asset',
				'parent_id' => 0
			),
			array(
				'account_code' => '2312',
				'name_ar' => 'ضريبة الدمغة',
				'name_en' => 'Stamp Tax',
				'account_type' => 'liability',
				'parent_id' => 0
			)
		);

		$added_count = 0;
		foreach ($tax_accounts as $account) {
			// فحص إذا كان الحساب موجود
			$existing = $this->model_accounts_chartaccount->getAccountByCode($account['account_code']);
			if (!$existing) {
				$this->model_accounts_chartaccount->addAccount($account);
				$added_count++;
			}
		}

		// تسجيل العملية
		$this->central_service->logActivity('add_tax_accounts', 'accounts',
			'إضافة الحسابات الضريبية المصرية (' . $added_count . ' حساب)', [
			'user_id' => $this->user->getId(),
			'added_count' => $added_count
		]);

		$this->session->data['success'] = sprintf($this->language->get('text_tax_accounts_added'), $added_count);
		$this->response->redirect($this->url->link('accounts/chartaccount', 'user_token=' . $this->session->data['user_token'], true));
	}

	/**
	 * دالة تنظيف المخرجات (CONSTITUTIONAL REQUIREMENT)
	 * Sanitize all output data to prevent XSS attacks
	 */
	private function sanitizeOutputData($data) {
		if (is_array($data)) {
			foreach ($data as $key => $value) {
				$data[$key] = $this->sanitizeOutputData($value);
			}
		} elseif (is_string($data)) {
			$data = htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
		}
		return $data;
	}

	/**
	 * Rate limiting check (Security Expert Recommendation)
	 * Prevent brute force attacks and excessive requests
	 */
	private function checkRateLimit() {
		$ip = $this->request->server['REMOTE_ADDR'];
		$current_time = time();
		$rate_limit_key = 'rate_limit_chartaccount_' . md5($ip);

		// Get current request count from session/cache
		$request_count = isset($this->session->data[$rate_limit_key]) ? $this->session->data[$rate_limit_key] : array();

		// Clean old requests (older than 1 hour)
		$request_count = array_filter($request_count, function($timestamp) use ($current_time) {
			return ($current_time - $timestamp) < 3600;
		});

		// Check if rate limit exceeded (max 100 requests per hour for accounting)
		if (count($request_count) >= 100) {
			$this->central_service->logActivity('rate_limit_exceeded', 'security',
				'Rate limit exceeded for chart of accounts access', [
				'ip_address' => $ip,
				'user_id' => $this->user->getId(),
				'timestamp' => $current_time
			]);

			// Return 429 Too Many Requests
			$this->response->addHeader('HTTP/1.1 429 Too Many Requests');
			$this->response->addHeader('Retry-After: 3600');
			$this->response->setOutput('Rate limit exceeded. Please try again later.');
			$this->response->output();
			exit;
		}

		// Add current request
		$request_count[] = $current_time;
		$this->session->data[$rate_limit_key] = $request_count;
	}

	/**
	 * Performance optimization (Performance Expert Recommendation)
	 * Optimize page loading and resource management
	 */
	private function optimizePagePerformance() {
		// Enable output compression
		if (!headers_sent() && extension_loaded('zlib')) {
			ob_start('ob_gzhandler');
		}

		// Set cache headers for static resources
		$this->response->addHeader('Cache-Control: public, max-age=3600'); // 1 hour for accounting data
		$this->response->addHeader('Expires: ' . gmdate('D, d M Y H:i:s', time() + 3600) . ' GMT');

		// Enable browser caching
		$etag = md5($this->request->server['REQUEST_URI'] . filemtime(__FILE__));
		$this->response->addHeader('ETag: "' . $etag . '"');

		// Check if client has cached version
		if (isset($this->request->server['HTTP_IF_NONE_MATCH']) &&
			$this->request->server['HTTP_IF_NONE_MATCH'] === '"' . $etag . '"') {
			$this->response->addHeader('HTTP/1.1 304 Not Modified');
			$this->response->output();
			exit;
		}

		// Preload critical resources
		$this->response->addHeader('Link: </view/stylesheet/accounts/chartaccount.css>; rel=preload; as=style');
		$this->response->addHeader('Link: </view/javascript/accounts/chartaccount.js>; rel=preload; as=script');

		// Memory optimization
		if (function_exists('memory_get_usage')) {
			$initial_memory = memory_get_usage();
			register_shutdown_function(function() use ($initial_memory) {
				$final_memory = memory_get_usage();
				$peak_memory = memory_get_peak_usage();
				error_log("Chart of Accounts Memory Usage - Initial: {$initial_memory}, Final: {$final_memory}, Peak: {$peak_memory}");
			});
		}
	}
}
