<?php
/**
 * تحكم ميزان المراجعة الشامل والمتكامل
 * يجمع بين البساطة والقوة - يتفوق على SAP وOracle في سهولة الاستخدام
 * مع الاحتفاظ بالقوة والمرونة المطلوبة للشركات الكبيرة
 */
class ControllerAccountsTrialBalance extends Controller {
    private $error = array();
    private $central_service;

    public function __construct($registry) {
        parent::__construct($registry);

        // تحميل الخدمات المركزية
        $this->load->model('core/central_service_manager');
        $this->central_service = $this->model_core_central_service_manager;
    }

    public function index() {
        // Enhanced security headers (Security Expert Recommendation)
        $this->response->addHeader('X-Content-Type-Options: nosniff');
        $this->response->addHeader('X-Frame-Options: SAMEORIGIN');
        $this->response->addHeader('X-XSS-Protection: 1; mode=block');
        $this->response->addHeader('Referrer-Policy: strict-origin-when-cross-origin');

        // Rate limiting check (Security Expert Recommendation)
        $this->checkRateLimit();

        // Performance optimization (Performance Expert Recommendation)
        $this->optimizePagePerformance();

        // فحص الصلاحيات المزدوجة
        if (!$this->user->hasPermission('access', 'accounts/trial_balance') ||
            !$this->user->hasKey('accounting_trial_balance_view')) {

            $this->central_service->logActivity('unauthorized_access', 'accounts',
                'محاولة وصول غير مصرح بها لميزان المراجعة', [
                'user_id' => $this->user->getId(),
                'ip_address' => $this->request->server['REMOTE_ADDR']
            ]);

            $this->response->redirect($this->url->link('error/permission'));
            return;
        }

        $this->load->language('accounts/trial_balance');
        $this->document->setTitle($this->language->get('heading_title'));

        // إضافة JavaScript والـ CSS المتقدم مع تحسين الأداء
        $this->document->addStyle('view/stylesheet/accounts/trial_balance.css?v=' . filemtime(DIR_APPLICATION . 'view/stylesheet/accounts/trial_balance.css'));
        $this->document->addScript('view/javascript/accounts/trial_balance.js?v=' . filemtime(DIR_APPLICATION . 'view/javascript/accounts/trial_balance.js'));
        $this->document->addScript('view/javascript/jquery/accounting.min.js');
        $this->document->addScript('view/javascript/jquery/select2.min.js');
        $this->document->addStyle('view/javascript/jquery/select2.min.css');
        $this->document->addScript('view/javascript/jquery/daterangepicker.min.js');
        $this->document->addStyle('view/javascript/jquery/daterangepicker.css');

        // تسجيل الوصول للشاشة
        $this->central_service->logActivity('view', 'accounts',
            'عرض شاشة ميزان المراجعة', [
            'user_id' => $this->user->getId(),
            'screen' => 'accounts/trial_balance'
        ]);

        $data['action'] = $this->url->link('accounts/trial_balance/print', 'user_token=' . $this->session->data['user_token'], true);
        $this->load->model('accounts/chartaccount');
        $data['accounts'] = $this->model_accounts_chartaccount->getAccountsToEntry();

        // فلاتر متقدمة
        $data['account_types'] = array(
            'all' => $this->language->get('text_all_accounts'),
            'asset' => $this->language->get('text_assets'),
            'liability' => $this->language->get('text_liabilities'),
            'equity' => $this->language->get('text_equity'),
            'revenue' => $this->language->get('text_revenue'),
            'expense' => $this->language->get('text_expense')
        );

        $data['balance_filters'] = array(
            'all' => $this->language->get('text_all_balances'),
            'debit_only' => $this->language->get('text_debit_balances_only'),
            'credit_only' => $this->language->get('text_credit_balances_only'),
            'zero_balance' => $this->language->get('text_zero_balances'),
            'non_zero' => $this->language->get('text_non_zero_balances')
        );

        $data['heading_title'] = $this->language->get('heading_title');
        $data['text_no_results'] = $this->language->get('text_no_results');

        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');

        $this->response->setOutput($this->load->view('accounts/trial_balance_form', $data));
    }

    /**
     * توليد ميزان المراجعة المتقدم (مدمج من advanced)
     */
    public function generate() {
        // فحص الصلاحيات المزدوجة
        if (!$this->user->hasPermission('access', 'accounts/trial_balance') ||
            !$this->user->hasKey('accounting_trial_balance_generate')) {

            $this->central_service->logActivity('unauthorized_generate', 'accounts',
                'محاولة توليد ميزان مراجعة غير مصرح بها', [
                'user_id' => $this->user->getId(),
                'action' => 'generate_trial_balance'
            ]);

            $this->response->redirect($this->url->link('error/permission'));
            return;
        }

        $this->load->language('accounts/trial_balance');
        $this->load->model('accounts/trial_balance');

        if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validateForm()) {
            try {
                $filter_data = $this->prepareFilterData();

                // تسجيل توليد التقرير
                $this->central_service->logActivity('generate_report', 'accounts',
                    'توليد ميزان المراجعة للفترة: ' . $filter_data['date_start'] . ' إلى ' . $filter_data['date_end'], [
                    'user_id' => $this->user->getId(),
                    'date_start' => $filter_data['date_start'],
                    'date_end' => $filter_data['date_end'],
                    'account_range' => $filter_data['account_start'] . ' - ' . $filter_data['account_end']
                ]);

                $trial_balance_data = $this->model_accounts_trial_balance->generateTrialBalance($filter_data);

                // إرسال إشعار للمحاسب الرئيسي
                $this->central_service->sendNotification(
                    'trial_balance_generated',
                    'توليد ميزان المراجعة',
                    'تم توليد ميزان المراجعة للفترة ' . $filter_data['date_start'] . ' إلى ' . $filter_data['date_end'] . ' بواسطة ' . $this->user->getFirstName(),
                    [$this->config->get('config_chief_accountant_id')],
                    [
                        'period' => $filter_data['date_start'] . ' - ' . $filter_data['date_end'],
                        'user_name' => $this->user->getFirstName() . ' ' . $this->user->getLastName(),
                        'accounts_count' => count($trial_balance_data)
                    ]
                );

                $this->session->data['success'] = $this->language->get('text_success_generate');
                $this->session->data['trial_balance_data'] = $trial_balance_data;
                $this->session->data['trial_balance_filter'] = $filter_data;

                // إعادة توجيه حسب الإجراء المطلوب
                if (isset($this->request->post['generate_and_new'])) {
                    $this->response->redirect($this->url->link('accounts/trial_balance', 'user_token=' . $this->session->data['user_token'], true));
                } elseif (isset($this->request->post['generate_and_export'])) {
                    $this->response->redirect($this->url->link('accounts/trial_balance/export', 'format=excel&user_token=' . $this->session->data['user_token'], true));
                } else {
                    $this->response->redirect($this->url->link('accounts/trial_balance/view', 'user_token=' . $this->session->data['user_token'], true));
                }
            } catch (Exception $e) {
                $this->error['warning'] = $e->getMessage();
            }
        }

        $this->getForm();
    }

    /**
     * عرض ميزان المراجعة
     */
    public function view() {
        // فحص الصلاحيات المزدوجة
        if (!$this->user->hasPermission('access', 'accounts/trial_balance') ||
            !$this->user->hasKey('accounting_trial_balance_view')) {

            $this->response->redirect($this->url->link('error/permission'));
            return;
        }

        $this->load->language('accounts/trial_balance');
        $this->document->setTitle($this->language->get('heading_title'));

        if (!isset($this->session->data['trial_balance_data'])) {
            $this->session->data['error'] = $this->language->get('error_no_data');
            $this->response->redirect($this->url->link('accounts/trial_balance', 'user_token=' . $this->session->data['user_token'], true));
            return;
        }

        $data = $this->session->data['trial_balance_data'];

        // تسجيل عرض التقرير
        $this->central_service->logActivity('view_report', 'accounts',
            'عرض ميزان المراجعة', [
            'user_id' => $this->user->getId(),
            'action' => 'view_trial_balance'
        ]);

        $data['breadcrumbs'] = array();
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('text_home'),
            'href' => $this->url->link('common/dashboard', 'user_token=' . $this->session->data['user_token'], true)
        );
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('heading_title'),
            'href' => $this->url->link('accounts/trial_balance', 'user_token=' . $this->session->data['user_token'], true)
        );

        $data['export_excel'] = $this->url->link('accounts/trial_balance/export', 'format=excel&user_token=' . $this->session->data['user_token'], true);
        $data['export_pdf'] = $this->url->link('accounts/trial_balance/export', 'format=pdf&user_token=' . $this->session->data['user_token'], true);
        $data['export_csv'] = $this->url->link('accounts/trial_balance/export', 'format=csv&user_token=' . $this->session->data['user_token'], true);

        $data['user_token'] = $this->session->data['user_token'];
        $data['heading_title'] = $this->language->get('heading_title');

        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');

        $this->response->setOutput($this->load->view('accounts/trial_balance_view', $data));
    }

    public function print() {
        $this->load->language('accounts/trial_balance');
        $this->load->model('accounts/trial_balance');
    
        $data['title'] = $this->language->get('print_title');
        $data['printdate'] = date('Y-m-d H:i:s');
        $data['user_token'] = $this->session->data['user_token'];
        $data['lang'] = $this->language->get('code');
        $data['direction'] = $this->language->get('direction');  
        $data['whoprint'] = $this->user->getUserName();
    
        // Handling dates with defaults
        $date_start = $this->request->post['date_start'] ?: date('Y-01-01'); // Default to start of current year
        $date_end = $this->request->post['date_end'] ?: date('Y-m-d'); // Default to today
    
        $data['start_date'] = date($this->language->get('date_format_short'), strtotime($date_start));
        $data['end_date'] = date($this->language->get('date_format_short'), strtotime($date_end));
    
        // Handling accounts with defaults
        $account_start = $this->request->post['account_start'] ?: $this->model_accounts_trial_balance->getMinAccountCode();
        $account_end = $this->request->post['account_end'] ?: $this->model_accounts_trial_balance->getMaxAccountCode();
    
        if ($account_start && $account_end && $date_start && $date_end) {
            $data['accounts'] = $this->model_accounts_trial_balance->getAccountRangeData($date_start, $date_end, $account_start, $account_end);
        } else {
            $data['accounts'] = [];
            $this->session->data['error'] = $this->language->get('error_no_data');
            $this->response->redirect($this->url->link('accounts/trial_balance', 'user_token=' . $this->session->data['user_token'], true));
            return;
        }
    
        $this->response->setOutput($this->load->view('accounts/trial_balance_list', $data));
    }

    /**
     * تصدير ميزان المراجعة
     */
    public function export() {
        // فحص الصلاحيات المزدوجة
        if (!$this->user->hasPermission('access', 'accounts/trial_balance') ||
            !$this->user->hasKey('accounting_trial_balance_export')) {

            $this->central_service->logActivity('unauthorized_export', 'accounts',
                'محاولة تصدير ميزان مراجعة غير مصرح بها', [
                'user_id' => $this->user->getId(),
                'action' => 'export_trial_balance'
            ]);

            $this->response->redirect($this->url->link('error/permission'));
            return;
        }

        $this->load->language('accounts/trial_balance');

        if (!isset($this->session->data['trial_balance_data'])) {
            $this->session->data['error'] = $this->language->get('error_no_data');
            $this->response->redirect($this->url->link('accounts/trial_balance', 'user_token=' . $this->session->data['user_token'], true));
            return;
        }

        $format = isset($this->request->get['format']) ? $this->db->escape($this->request->get['format']) : 'excel';
        $trial_balance_data = $this->session->data['trial_balance_data'];
        $filter_data = $this->session->data['trial_balance_filter'] ?? array();

        // تسجيل عملية التصدير
        $this->central_service->logActivity('export', 'accounts',
            'تصدير ميزان المراجعة - ' . strtoupper($format), [
            'user_id' => $this->user->getId(),
            'format' => $format,
            'period' => ($filter_data['date_start'] ?? '') . ' - ' . ($filter_data['date_end'] ?? '')
        ]);

        // إرسال إشعار للمحاسب الرئيسي
        $this->central_service->sendNotification(
            'trial_balance_exported',
            'تصدير ميزان المراجعة',
            'تم تصدير ميزان المراجعة بصيغة ' . strtoupper($format) . ' بواسطة ' . $this->user->getFirstName(),
            [$this->config->get('config_chief_accountant_id')],
            [
                'format' => $format,
                'period' => ($filter_data['date_start'] ?? '') . ' - ' . ($filter_data['date_end'] ?? ''),
                'user_name' => $this->user->getFirstName() . ' ' . $this->user->getLastName()
            ]
        );

        switch ($format) {
            case 'excel':
                $this->exportToExcel($trial_balance_data);
                break;
            case 'pdf':
                $this->exportToPdf($trial_balance_data);
                break;
            case 'csv':
                $this->exportToCsv($trial_balance_data);
                break;
            default:
                $this->exportToExcel($trial_balance_data);
        }
    }

    /**
     * دالة التحقق من صحة البيانات (من advanced)
     */
    protected function validateForm() {
        if (!$this->user->hasPermission('access', 'accounts/trial_balance')) {
            $this->error['warning'] = $this->language->get('error_permission');
        }

        if (empty($this->request->post['date_start'])) {
            $this->error['date_start'] = $this->language->get('error_date_start');
        }

        if (empty($this->request->post['date_end'])) {
            $this->error['date_end'] = $this->language->get('error_date_end');
        }

        if (!empty($this->request->post['date_start']) && !empty($this->request->post['date_end'])) {
            if (strtotime($this->request->post['date_start']) > strtotime($this->request->post['date_end'])) {
                $this->error['date_range'] = $this->language->get('error_date_range');
            }
        }

        return !$this->error;
    }

    /**
     * إعداد بيانات الفلترة (من advanced)
     */
    protected function prepareFilterData() {
        $filter_data = array();

        $filter_data['date_start'] = isset($this->request->post['date_start']) ? $this->db->escape($this->request->post['date_start']) : date('Y-01-01');
        $filter_data['date_end'] = isset($this->request->post['date_end']) ? $this->db->escape($this->request->post['date_end']) : date('Y-m-d');
        $filter_data['account_start'] = isset($this->request->post['account_start']) ? $this->db->escape($this->request->post['account_start']) : '';
        $filter_data['account_end'] = isset($this->request->post['account_end']) ? $this->db->escape($this->request->post['account_end']) : '';
        $filter_data['branch_id'] = isset($this->request->post['branch_id']) ? (int)$this->request->post['branch_id'] : 0;
        $filter_data['cost_center_id'] = isset($this->request->post['cost_center_id']) ? (int)$this->request->post['cost_center_id'] : 0;
        $filter_data['include_zero_balance'] = isset($this->request->post['include_zero_balance']) ?
                                               (bool)$this->request->post['include_zero_balance'] : false;
        $filter_data['comparison_period'] = isset($this->request->post['comparison_period']) ? $this->db->escape($this->request->post['comparison_period']) : '';

        return $filter_data;
    }

    /**
     * عرض النموذج (من advanced)
     */
    protected function getForm() {
        $data['text_form'] = $this->language->get('text_form');

        if (isset($this->error['warning'])) {
            $data['error_warning'] = $this->error['warning'];
        } else {
            $data['error_warning'] = '';
        }

        if (isset($this->error['date_start'])) {
            $data['error_date_start'] = $this->error['date_start'];
        } else {
            $data['error_date_start'] = '';
        }

        if (isset($this->error['date_end'])) {
            $data['error_date_end'] = $this->error['date_end'];
        } else {
            $data['error_date_end'] = '';
        }

        $data['action'] = $this->url->link('accounts/trial_balance/generate', 'user_token=' . $this->session->data['user_token'], true);

        // تحميل قوائم البيانات
        $this->load->model('accounts/chartaccount');
        $this->load->model('branch/branch');

        $data['accounts'] = $this->model_accounts_chartaccount->getAccountsToEntry();
        $data['branches'] = $this->model_branch_branch->getBranches();

        // القيم الافتراضية
        $data['date_start'] = $this->request->post['date_start'] ?? date('Y-01-01');
        $data['date_end'] = $this->request->post['date_end'] ?? date('Y-m-d');
        $data['account_start'] = $this->request->post['account_start'] ?? '';
        $data['account_end'] = $this->request->post['account_end'] ?? '';
        $data['branch_id'] = $this->request->post['branch_id'] ?? 0;
        $data['include_zero_balance'] = $this->request->post['include_zero_balance'] ?? false;

        $data['user_token'] = $this->session->data['user_token'];
        $data['heading_title'] = $this->language->get('heading_title');
        $data['text_no_results'] = $this->language->get('text_no_results');

        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');

        $this->response->setOutput($this->load->view('accounts/trial_balance_form', $data));
    }



    /**
     * مقارنة ميزان المراجعة (مدمج من advanced)
     */
    public function compare() {
        // فحص الصلاحيات المزدوجة
        if (!$this->user->hasPermission('access', 'accounts/trial_balance') ||
            !$this->user->hasKey('accounting_trial_balance_compare')) {

            $this->central_service->logActivity('unauthorized_compare', 'accounts',
                'محاولة مقارنة ميزان مراجعة غير مصرح بها', [
                'user_id' => $this->user->getId(),
                'action' => 'compare_trial_balance'
            ]);

            $json['error'] = $this->language->get('error_permission');
            $this->response->addHeader('Content-Type: application/json');
            $this->response->setOutput(json_encode($json));
            return;
        }

        $this->load->language('accounts/trial_balance');
        $this->load->model('accounts/trial_balance');

        $json = array();

        if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validateCompareForm()) {
            try {
                $compare_data = $this->prepareCompareData();
                $comparison_result = $this->model_accounts_trial_balance->compareTrialBalances($compare_data);

                // تسجيل عملية المقارنة
                $this->central_service->logActivity('compare', 'accounts',
                    'مقارنة ميزان المراجعة بين فترتين', [
                    'user_id' => $this->user->getId(),
                    'period1' => $compare_data['period1'],
                    'period2' => $compare_data['period2']
                ]);

                $json['success'] = $this->language->get('text_success_compare');
                $json['comparison_data'] = $comparison_result;
            } catch (Exception $e) {
                $json['error'] = $e->getMessage();
            }
        } else {
            $json['error'] = $this->language->get('error_form');
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    /**
     * التفصيل للحساب (drill-down) - مدمج من advanced
     */
    public function drillDown() {
        $this->load->model('accounts/trial_balance');

        $json = array();

        if (isset($this->request->get['account_id']) && isset($this->request->get['date_start']) && isset($this->request->get['date_end'])) {
            $account_id = (int)$this->request->get['account_id'];
            $date_start = $this->request->get['date_start'];
            $date_end = $this->request->get['date_end'];

            // تسجيل عملية التفصيل
            $this->central_service->logActivity('drill_down', 'accounts',
                'تفصيل حساب في ميزان المراجعة', [
                'user_id' => $this->user->getId(),
                'account_id' => $account_id,
                'date_range' => $date_start . ' - ' . $date_end
            ]);

            $drill_down_data = $this->model_accounts_trial_balance->getDrillDownData($account_id, $date_start, $date_end);

            // إضافة تفاصيل إضافية
            $json['success'] = true;
            $json['account_details'] = array(
                'opening_balance' => $drill_down_data['opening_balance'] ?? 0,
                'total_debits' => $drill_down_data['total_debits'] ?? 0,
                'total_credits' => $drill_down_data['total_credits'] ?? 0,
                'closing_balance' => $drill_down_data['closing_balance'] ?? 0,
                'transaction_count' => count($drill_down_data['transactions'] ?? array())
            );
            $json['data'] = $drill_down_data;
        } else {
            $json['error'] = $this->language->get('error_missing_data');
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    /**
     * التحقق من صحة بيانات المقارنة
     */
    protected function validateCompareForm() {
        if (empty($this->request->post['period1_start']) || empty($this->request->post['period1_end'])) {
            $this->error['period1'] = $this->language->get('error_period1');
        }

        if (empty($this->request->post['period2_start']) || empty($this->request->post['period2_end'])) {
            $this->error['period2'] = $this->language->get('error_period2');
        }

        return !$this->error;
    }

    /**
     * إعداد بيانات المقارنة
     */
    protected function prepareCompareData() {
        return array(
            'period1' => array(
                'start' => $this->request->post['period1_start'],
                'end' => $this->request->post['period1_end']
            ),
            'period2' => array(
                'start' => $this->request->post['period2_start'],
                'end' => $this->request->post['period2_end']
            ),
            'account_range' => array(
                'start' => $this->request->post['account_start'] ?? '',
                'end' => $this->request->post['account_end'] ?? ''
            )
        );
    }

    /**
     * تصدير إلى Excel
     */
    private function exportToExcel($data) {
        $filename = 'trial_balance_' . date('Y-m-d') . '.xls';

        header('Content-Type: application/vnd.ms-excel');
        header('Content-Disposition: attachment;filename="' . $filename . '"');
        header('Cache-Control: max-age=0');

        echo '<table border="1">';
        echo '<tr><th>' . $this->language->get('column_account_code') . '</th>';
        echo '<th>' . $this->language->get('column_account_name') . '</th>';
        echo '<th>' . $this->language->get('column_debit_balance') . '</th>';
        echo '<th>' . $this->language->get('column_credit_balance') . '</th></tr>';

        foreach ($data as $account) {
            echo '<tr>';
            echo '<td>' . $account['account_code'] . '</td>';
            echo '<td>' . $account['account_name'] . '</td>';
            echo '<td>' . number_format($account['debit_balance'], 2) . '</td>';
            echo '<td>' . number_format($account['credit_balance'], 2) . '</td>';
            echo '</tr>';
        }

        echo '</table>';
        exit;
    }

    /**
     * تصدير إلى PDF
     */
    private function exportToPdf($data) {
        require_once(DIR_SYSTEM . 'library/tcpdf/tcpdf.php');

        $pdf = new TCPDF('P', 'mm', 'A4', true, 'UTF-8');
        $pdf->SetCreator('AYM ERP System');
        $pdf->SetAuthor($this->config->get('config_name'));
        $pdf->SetTitle($this->language->get('heading_title'));

        $pdf->AddPage();
        $pdf->SetFont('dejavusans', 'B', 16);
        $pdf->Cell(0, 10, $this->language->get('heading_title'), 0, 1, 'C');

        $pdf->SetFont('dejavusans', '', 10);
        $html = '<table border="1" cellpadding="4">';
        $html .= '<tr style="background-color:#f0f0f0;">';
        $html .= '<th>' . $this->language->get('column_account_code') . '</th>';
        $html .= '<th>' . $this->language->get('column_account_name') . '</th>';
        $html .= '<th>' . $this->language->get('column_debit_balance') . '</th>';
        $html .= '<th>' . $this->language->get('column_credit_balance') . '</th>';
        $html .= '</tr>';

        foreach ($data as $account) {
            $html .= '<tr>';
            $html .= '<td>' . $account['account_code'] . '</td>';
            $html .= '<td>' . $account['account_name'] . '</td>';
            $html .= '<td>' . number_format($account['debit_balance'], 2) . '</td>';
            $html .= '<td>' . number_format($account['credit_balance'], 2) . '</td>';
            $html .= '</tr>';
        }

        $html .= '</table>';
        $pdf->writeHTML($html, true, false, true, false, '');

        $pdf->Output('trial_balance_' . date('Y-m-d') . '.pdf', 'D');
        exit;
    }

    /**
     * تصدير إلى CSV
     */
    private function exportToCsv($data) {
        $filename = 'trial_balance_' . date('Y-m-d') . '.csv';

        header('Content-Type: text/csv; charset=utf-8');
        header('Content-Disposition: attachment;filename="' . $filename . '"');
        header('Cache-Control: max-age=0');

        $output = fopen('php://output', 'w');

        // إضافة BOM للدعم العربي
        fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));

        // رؤوس الأعمدة
        fputcsv($output, array(
            $this->language->get('column_account_code'),
            $this->language->get('column_account_name'),
            $this->language->get('column_debit_balance'),
            $this->language->get('column_credit_balance')
        ));

        // البيانات
        foreach ($data as $account) {
            fputcsv($output, array(
                $account['account_code'],
                $account['account_name'],
                number_format($account['debit_balance'], 2),
                number_format($account['credit_balance'], 2)
            ));
        }

        fclose($output);
        exit;
    }

    /**
     * التحليل المتقدم لميزان المراجعة مع فحص التوازن والانحرافات
     */
    public function advancedAnalysis() {
        // فحص الصلاحيات المزدوجة
        if (!$this->user->hasPermission('access', 'accounts/trial_balance') ||
            !$this->user->hasKey('accounting_trial_balance_analysis')) {

            $this->central_service->logActivity('unauthorized_access', 'accounts',
                'محاولة الوصول للتحليل المتقدم لميزان المراجعة غير مصرح بها', [
                'user_id' => $this->user->getId(),
                'ip_address' => $this->request->server['REMOTE_ADDR']
            ]);

            $this->response->redirect($this->url->link('error/permission'));
            return;
        }

        $this->load->language('accounts/trial_balance');
        $this->document->setTitle($this->language->get('heading_title') . ' - ' . $this->language->get('text_advanced_analysis'));
        $this->load->model('accounts/trial_balance');

        // إضافة CSS و JavaScript المتقدم للتحليل
        $this->document->addStyle('view/stylesheet/accounts/trial_balance_analysis.css');
        $this->document->addScript('view/javascript/accounts/trial_balance_analysis.js');
        $this->document->addScript('view/javascript/jquery/chart.min.js');

        if ($this->request->server['REQUEST_METHOD'] == 'POST') {
            try {
                $date_start = $this->request->post['date_start'];
                $date_end = $this->request->post['date_end'];

                // إعداد فترة المقارنة إذا طُلبت
                $comparison_period = null;
                if (!empty($this->request->post['comparison_period'])) {
                    $comparison_period = array(
                        'date_start' => $this->request->post['comparison_date_start'],
                        'date_end' => $this->request->post['comparison_date_end']
                    );
                }

                // تسجيل إنشاء التحليل المتقدم
                $this->central_service->logActivity('generate_advanced_trial_balance_analysis', 'accounts',
                    'إنشاء تحليل متقدم لميزان المراجعة للفترة: ' . $date_start . ' إلى ' . $date_end, [
                    'user_id' => $this->user->getId(),
                    'date_start' => $date_start,
                    'date_end' => $date_end,
                    'has_comparison' => !empty($comparison_period)
                ]);

                // الحصول على التحليل المتقدم
                $advanced_analysis = $this->model_accounts_trial_balance->getAdvancedTrialBalanceAnalysis(
                    $date_start, $date_end, $comparison_period
                );

                // إرسال إشعار للإدارة المالية
                $this->central_service->sendNotification(
                    'advanced_trial_balance_analysis_generated',
                    'تحليل متقدم لميزان المراجعة',
                    'تم إنشاء تحليل متقدم لميزان المراجعة للفترة ' . $date_start . ' إلى ' . $date_end,
                    [$this->config->get('config_cfo_id'), $this->config->get('config_chief_accountant_id')],
                    [
                        'date_start' => $date_start,
                        'date_end' => $date_end,
                        'total_accounts' => $advanced_analysis['account_analysis']['total_accounts'],
                        'is_balanced' => $advanced_analysis['balance_verification']['is_balanced'],
                        'balance_quality_score' => $advanced_analysis['balance_verification']['balance_quality_score'],
                        'generated_by' => $this->user->getFirstName() . ' ' . $this->user->getLastName()
                    ]
                );

                $this->session->data['success'] = $this->language->get('text_success_analysis');
                $this->session->data['advanced_trial_balance_analysis'] = $advanced_analysis;

                $this->response->redirect($this->url->link('accounts/trial_balance/analysisView', 'user_token=' . $this->session->data['user_token'], true));
            } catch (Exception $e) {
                $this->error['warning'] = $e->getMessage();
            }
        }

        // عرض نموذج التحليل المتقدم
        $data['breadcrumbs'] = array();
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('text_home'),
            'href' => $this->url->link('common/dashboard', 'user_token=' . $this->session->data['user_token'], true)
        );
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('heading_title'),
            'href' => $this->url->link('accounts/trial_balance', 'user_token=' . $this->session->data['user_token'], true)
        );
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('text_advanced_analysis'),
            'href' => $this->url->link('accounts/trial_balance/advancedAnalysis', 'user_token=' . $this->session->data['user_token'], true)
        );

        $data['action'] = $this->url->link('accounts/trial_balance/advancedAnalysis', 'user_token=' . $this->session->data['user_token'], true);
        $data['cancel'] = $this->url->link('accounts/trial_balance', 'user_token=' . $this->session->data['user_token'], true);

        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');

        $this->response->setOutput($this->load->view('accounts/trial_balance_advanced_analysis_form', $data));
    }

    /**
     * عرض التحليل المتقدم
     */
    public function analysisView() {
        // فحص الصلاحيات المزدوجة
        if (!$this->user->hasPermission('access', 'accounts/trial_balance') ||
            !$this->user->hasKey('accounting_trial_balance_view')) {

            $this->response->redirect($this->url->link('error/permission'));
            return;
        }

        $this->load->language('accounts/trial_balance');

        if (!isset($this->session->data['advanced_trial_balance_analysis'])) {
            $this->session->data['error'] = $this->language->get('error_no_analysis_data');
            $this->response->redirect($this->url->link('accounts/trial_balance', 'user_token=' . $this->session->data['user_token'], true));
            return;
        }

        // تسجيل عرض التحليل
        $this->central_service->logActivity('view_advanced_trial_balance_analysis', 'accounts',
            'عرض التحليل المتقدم لميزان المراجعة', [
            'user_id' => $this->user->getId()
        ]);

        $data = $this->session->data['advanced_trial_balance_analysis'];

        $data['breadcrumbs'] = array();
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('text_home'),
            'href' => $this->url->link('common/dashboard', 'user_token=' . $this->session->data['user_token'], true)
        );
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('heading_title'),
            'href' => $this->url->link('accounts/trial_balance', 'user_token=' . $this->session->data['user_token'], true)
        );
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('text_analysis_view'),
            'href' => $this->url->link('accounts/trial_balance/analysisView', 'user_token=' . $this->session->data['user_token'], true)
        );

        $data['export_excel'] = $this->url->link('accounts/trial_balance/export', 'user_token=' . $this->session->data['user_token'] . '&format=excel', true);
        $data['export_pdf'] = $this->url->link('accounts/trial_balance/export', 'user_token=' . $this->session->data['user_token'] . '&format=pdf', true);
        $data['print'] = $this->url->link('accounts/trial_balance/analysisPrint', 'user_token=' . $this->session->data['user_token'], true);
        $data['back'] = $this->url->link('accounts/trial_balance', 'user_token=' . $this->session->data['user_token'], true);

        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');

        $this->response->setOutput($this->load->view('accounts/trial_balance_advanced_analysis_view', $data));
    }

    /**
     * طباعة التحليل المتقدم
     */
    public function analysisPrint() {
        $this->load->language('accounts/trial_balance');

        if (!isset($this->session->data['advanced_trial_balance_analysis'])) {
            $this->response->redirect($this->url->link('accounts/trial_balance', 'user_token=' . $this->session->data['user_token'], true));
            return;
        }

        $data = $this->session->data['advanced_trial_balance_analysis'];
        $data['company_name'] = $this->config->get('config_name');
        $data['generated_date'] = date('Y-m-d H:i:s');
        $data['generated_by'] = $this->user->getFirstName() . ' ' . $this->user->getLastName();

        $this->response->setOutput($this->load->view('accounts/trial_balance_advanced_analysis_print', $data));
    }

    /**
     * فحص التوازن المحاسبي السريع (AJAX)
     */
    public function quickBalanceCheck() {
        $this->load->language('accounts/trial_balance');
        $this->load->model('accounts/trial_balance');

        $json = array();

        if ($this->request->server['REQUEST_METHOD'] == 'POST') {
            $date_start = $this->request->post['date_start'] ?? date('Y-m-01');
            $date_end = $this->request->post['date_end'] ?? date('Y-m-d');

            try {
                $trial_balance_data = $this->model_accounts_trial_balance->getAccountRangeData(
                    $date_start, $date_end,
                    $this->model_accounts_trial_balance->getMinAccountCode(),
                    $this->model_accounts_trial_balance->getMaxAccountCode()
                );

                $balance_check = $this->model_accounts_trial_balance->verifyAccountingBalance($trial_balance_data);

                $json['success'] = true;
                $json['balance_check'] = $balance_check;
                $json['message'] = $balance_check['is_balanced'] ?
                    $this->language->get('text_balance_verified') :
                    $this->language->get('text_balance_unverified');

            } catch (Exception $e) {
                $json['success'] = false;
                $json['error'] = $e->getMessage();
            }
        } else {
            $json['success'] = false;
            $json['error'] = $this->language->get('error_invalid_request');
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    /**
     * دالة تنظيف المخرجات (CONSTITUTIONAL REQUIREMENT)
     * Sanitize all output data to prevent XSS attacks
     */
    private function sanitizeOutputData($data) {
        if (is_array($data)) {
            foreach ($data as $key => $value) {
                $data[$key] = $this->sanitizeOutputData($value);
            }
        } elseif (is_string($data)) {
            $data = htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
        }
        return $data;
    }

    /**
     * Rate limiting check (Security Expert Recommendation)
     * Prevent brute force attacks and excessive requests for trial balance
     */
    private function checkRateLimit() {
        $ip = $this->request->server['REMOTE_ADDR'];
        $current_time = time();
        $rate_limit_key = 'rate_limit_trial_balance_' . md5($ip);

        // Get current request count from session/cache
        $request_count = isset($this->session->data[$rate_limit_key]) ? $this->session->data[$rate_limit_key] : array();

        // Clean old requests (older than 1 hour)
        $request_count = array_filter($request_count, function($timestamp) use ($current_time) {
            return ($current_time - $timestamp) < 3600;
        });

        // Check if rate limit exceeded (max 50 requests per hour for trial balance)
        if (count($request_count) >= 50) {
            $this->central_service->logActivity('rate_limit_exceeded', 'security',
                'Rate limit exceeded for trial balance access', [
                'ip_address' => $ip,
                'user_id' => $this->user->getId(),
                'timestamp' => $current_time
            ]);

            // Return 429 Too Many Requests
            $this->response->addHeader('HTTP/1.1 429 Too Many Requests');
            $this->response->addHeader('Retry-After: 3600');
            $this->response->setOutput('Rate limit exceeded. Please try again later.');
            $this->response->output();
            exit;
        }

        // Add current request
        $request_count[] = $current_time;
        $this->session->data[$rate_limit_key] = $request_count;
    }

    /**
     * Performance optimization (Performance Expert Recommendation)
     * Optimize trial balance calculation and resource management
     */
    private function optimizePagePerformance() {
        // Enable output compression
        if (!headers_sent() && extension_loaded('zlib')) {
            ob_start('ob_gzhandler');
        }

        // Set cache headers for trial balance (longer cache due to less frequent changes)
        $this->response->addHeader('Cache-Control: public, max-age=7200'); // 2 hours for trial balance
        $this->response->addHeader('Expires: ' . gmdate('D, d M Y H:i:s', time() + 7200) . ' GMT');

        // Enable browser caching
        $etag = md5($this->request->server['REQUEST_URI'] . filemtime(__FILE__));
        $this->response->addHeader('ETag: "' . $etag . '"');

        // Check if client has cached version
        if (isset($this->request->server['HTTP_IF_NONE_MATCH']) &&
            $this->request->server['HTTP_IF_NONE_MATCH'] === '"' . $etag . '"') {
            $this->response->addHeader('HTTP/1.1 304 Not Modified');
            $this->response->output();
            exit;
        }

        // Preload critical resources for trial balance
        $this->response->addHeader('Link: </view/stylesheet/accounts/trial_balance.css>; rel=preload; as=style');
        $this->response->addHeader('Link: </view/javascript/accounts/trial_balance.js>; rel=preload; as=script');

        // Memory optimization for large trial balance calculations
        if (function_exists('memory_get_usage')) {
            $initial_memory = memory_get_usage();
            register_shutdown_function(function() use ($initial_memory) {
                $final_memory = memory_get_usage();
                $peak_memory = memory_get_peak_usage();
                error_log("Trial Balance Memory Usage - Initial: {$initial_memory}, Final: {$final_memory}, Peak: {$peak_memory}");
            });
        }

        // Set memory limit for large trial balance reports
        ini_set('memory_limit', '512M');

        // Set execution time limit for complex calculations
        set_time_limit(300); // 5 minutes for trial balance generation
    }
}
