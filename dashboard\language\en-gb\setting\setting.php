<?php
// Heading
$_['heading_title']                  = 'Settings';

$_['text_accounting_settings']           = 'Accounting Settings';
$_['entry_financial_year_start']         = 'Financial Year Start';
$_['entry_financial_year_end']           = 'Financial Year End';
$_['entry_lock_date']                    = 'Lock Date';

$_['text_currency_settings']             = 'Currency Settings';
$_['entry_default_currency']             = 'Default Currency';
$_['entry_currency_rounding']            = 'Currency Rounding';
$_['text_none']                          = 'None';
$_['text_round_nearest']                 = 'Round to Nearest';
$_['text_round_down']                    = 'Round Down';

$_['text_inventory_valuation']           = 'Inventory Valuation';
$_['entry_inventory_valuation_method']   = 'Inventory Valuation Method';
$_['text_weighted_average']              = 'Weighted Average';
$_['entry_inventory_adjustment_account'] = 'Inventory Adjustment Account';
$_['text_select']                        = 'Select';

$_['text_default_accounts']              = 'Default Accounts';
$_['entry_default_sales_account']        = 'Default Sales Account';
$_['entry_default_purchase_account']     = 'Default Purchase Account';
$_['entry_default_inventory_account']    = 'Default Inventory Account';
$_['entry_default_tax_account']          = 'Default Tax Account';
$_['entry_default_ar_account']           = 'Default Accounts Receivable Account';
$_['entry_default_ap_account']           = 'Default Accounts Payable Account';

$_['text_recognition_policies']          = 'Recognition Policies';
$_['entry_accounting_basis']             = 'Accounting Basis';
$_['text_accrual_basis']                 = 'Accrual Basis';
$_['text_cash_basis']                    = 'Cash Basis';

$_['text_reporting_preferences']         = 'Reporting Preferences';
$_['entry_reporting_period']             = 'Reporting Period';
$_['text_monthly']                       = 'Monthly';
$_['text_quarterly']                     = 'Quarterly';
$_['text_annually']                      = 'Annually';

$_['text_taxation_settings']             = 'Taxation Settings';
$_['entry_tax_regime']                   = 'Tax Regime';

$_['text_audit_control']                 = 'Audit Control';
$_['entry_lock_after_audit']             = 'Lock After Audit';
$_['text_yes']                           = 'Yes';
$_['text_no']                            = 'No';

// Missing Variables from Audit Report
$_['action'] = '';
$_['column_left'] = '';
$_['config_affiliate_commission'] = '';
$_['config_building_number'] = '';
$_['config_comment'] = '';
$_['config_compression'] = '';
$_['config_email'] = '';
$_['config_encryption'] = '';
$_['config_error_filename'] = '';
$_['config_eta_access_token'] = '';
$_['config_eta_activity_code'] = '';
$_['config_eta_client_id'] = '';
$_['config_eta_client_secret'] = '';
$_['config_eta_notification_email'] = '';
$_['config_eta_taxpayer_id'] = '';
$_['config_eta_usb_pin'] = '';
$_['config_fax'] = '';
$_['config_file_ext_allowed'] = '';
$_['config_file_max_size'] = '';
$_['config_file_mime_allowed'] = '';
$_['config_financial_year_end'] = '';
$_['config_financial_year_start'] = '';
$_['config_geocode'] = '';
$_['config_icon'] = '';
$_['config_image'] = '';
$_['config_invoice_prefix'] = '';
$_['config_language'] = '';
$_['config_layout_id'] = '';
$_['config_logo'] = '';
$_['config_mail_alert'] = '';
$_['config_mail_engine'] = '';
$_['config_mail_parameter'] = '';
$_['config_mail_smtp_hostname'] = '';
$_['config_mail_smtp_password'] = '';
$_['config_mail_smtp_port'] = '';
$_['config_mail_smtp_timeout'] = '';
$_['config_mail_smtp_username'] = '';
$_['config_meta_description'] = '';
$_['config_meta_title'] = '';
$_['config_name'] = '';
$_['config_open'] = '';
$_['config_owner'] = '';
$_['config_password'] = '';
$_['config_robots'] = '';
$_['config_secure'] = '';
$_['config_seo_url'] = '';
$_['config_shared'] = '';
$_['config_telephone'] = '';
$_['config_theme'] = '';
$_['config_url'] = '';
$_['footer'] = '';
$_['header'] = '';
$_['setting/setting'] = '';
$_['text_accounting'] = '';
$_['text_affiliate'] = '';
$_['text_api'] = '';
$_['text_captcha'] = '';
$_['text_compression'] = '';
$_['text_default'] = '';
$_['text_edit'] = '';
$_['text_ftp'] = '';
$_['text_general'] = '';
$_['text_image'] = '';
$_['text_local'] = '';
$_['text_mail'] = '';
$_['text_option'] = '';
$_['text_security'] = '';
$_['text_server'] = '';
$_['text_store'] = '';
$_['text_success'] = '';
$_['text_upload'] = '';
$_['user_token'] = '';


// Text
$_['text_stores']                    = 'Stores';
$_['text_success']                   = 'Success: You have modified settings!';
$_['text_edit']                      = 'Edit Setting';
$_['text_product']                   = 'Products';
$_['text_review']                    = 'Reviews';
$_['text_voucher']                   = 'Vouchers';
$_['text_tax']                       = 'Taxes';
$_['text_account']                   = 'Account';
$_['text_checkout']                  = 'Checkout';
$_['text_stock']                     = 'Stock';
$_['text_affiliate']                 = 'Affiliates';
$_['text_captcha']                   = 'Captcha';
$_['text_register']                  = 'Register';
$_['text_guest']                     = 'Guest Checkout';
$_['text_return']                    = 'Returns';
$_['text_contact']                   = 'Contact';
$_['text_shipping']                  = 'Shipping Address';
$_['text_payment']                   = 'Payment Address';
$_['text_mail']                      = 'Mail';
$_['text_smtp']                      = 'SMTP';
$_['text_mail_alert']                = 'Mail Alerts';
$_['text_mail_account']              = 'Register';
$_['text_mail_affiliate']            = 'Affiliate';
$_['text_mail_order']                = 'Orders';
$_['text_mail_review']               = 'Reviews';
$_['text_general']                   = 'General';
$_['text_security']                  = 'Security';
$_['text_upload']                    = 'Uploads';
$_['text_error']                     = 'Error Handling';

// Entry
$_['entry_meta_title']               = 'Meta Title';
$_['entry_meta_description']         = 'Meta Tag Description';
$_['entry_meta_keyword']             = 'Meta Tag Keywords';
$_['entry_layout']                   = 'Default Layout';
$_['entry_theme']                    = 'Theme';
$_['entry_name']                     = 'Store Name';
$_['entry_owner']                    = 'Store Owner';
$_['entry_address']                  = 'Address';
$_['entry_geocode']                  = 'Geocode';
$_['entry_email']                    = 'E-Mail';
$_['entry_telephone']                = 'Telephone';
$_['entry_fax']                      = 'Fax';
$_['entry_image']                    = 'Image';
$_['entry_open']                     = 'Opening Times';
$_['entry_comment']                  = 'Comment';
$_['entry_location']                 = 'Store Location';
$_['entry_country']                  = 'Country';
$_['entry_zone']                     = 'Region / State';
$_['entry_timezone']                 = 'Time Zone';
$_['entry_language']                 = 'Language';
$_['entry_admin_language']           = 'Administration Language';
$_['entry_currency']                 = 'Currency';
$_['entry_currency_auto']            = 'Auto Update Currency';
$_['entry_currency_engine']          = 'Currency Rate Engine';
$_['entry_length_class']             = 'Length Class';
$_['entry_weight_class']             = 'Weight Class';
$_['entry_limit_admin']              = 'Default Items Per Page (Admin)';
$_['entry_product_count']            = 'Category Product Count';
$_['entry_review']                   = 'Allow Reviews';
$_['entry_review_guest']             = 'Allow Guest Reviews';
$_['entry_voucher_min']              = 'Voucher Min';
$_['entry_voucher_max']              = 'Voucher Max';
$_['entry_tax']                      = 'Display Prices With Tax';
$_['entry_tax_default']              = 'Use Store Tax Address';
$_['entry_tax_customer']             = 'Use Customer Tax Address';
$_['entry_customer_online']          = 'Customers Online';
$_['entry_customer_activity']        = 'Customers Activity';
$_['entry_customer_search']          = 'Log Customer Searches';
$_['entry_customer_group']           = 'Customer Group';
$_['entry_customer_group_display']   = 'Customer Groups';
$_['entry_customer_price']           = 'Login Display Prices';
$_['entry_login_attempts']           = 'Max Login Attempts';
$_['entry_account']                  = 'Account Terms';
$_['entry_cart_weight']              = 'Display Weight on Cart Page';
$_['entry_checkout_guest']           = 'Guest Checkout';
$_['entry_checkout']                 = 'Checkout Terms';
$_['entry_invoice_prefix']           = 'Invoice Prefix';
$_['entry_order_status']             = 'Order Status';
$_['entry_processing_status']        = 'Processing Order Status';
$_['entry_complete_status']          = 'Complete Order Status';
$_['entry_fraud_status']             = 'Fraud Order Status';
$_['entry_api']                      = 'API User';
$_['entry_stock_display']            = 'Display Stock';
$_['entry_stock_warning']            = 'Show Out Of Stock Warning';
$_['entry_stock_checkout']           = 'Stock Checkout';
$_['entry_affiliate_group']          = 'Affiliate Group';
$_['entry_affiliate_approval']       = 'Affiliate Requires Approval';
$_['entry_affiliate_auto']           = 'Automatic Commission';
$_['entry_affiliate_commission']     = 'Affiliate Commission (%)';
$_['entry_affiliate']                = 'Affiliate Terms';
$_['entry_return']                   = 'Return Terms';
$_['entry_return_status']            = 'Return Status';
$_['entry_captcha']                  = 'Captcha';
$_['entry_captcha_page']             = 'Captcha Page';
$_['entry_logo']                     = 'Store Logo';
$_['entry_icon']                     = 'Icon';
$_['entry_mail_engine']              = 'Mail Engine';
$_['entry_mail_parameter']           = 'Mail Parameters';
$_['entry_mail_smtp_hostname']       = 'SMTP Hostname';
$_['entry_mail_smtp_username']       = 'SMTP Username';
$_['entry_mail_smtp_password']       = 'SMTP Password';
$_['entry_mail_smtp_port']           = 'SMTP Port';
$_['entry_mail_smtp_timeout']        = 'SMTP Timeout';
$_['entry_mail_alert']               = 'Alert Mail';
$_['entry_mail_alert_email']         = 'Additional Alert Mail';
$_['entry_secure']                   = 'Use SSL';
$_['entry_shared']                   = 'Use Shared Sessions';
$_['entry_robots']                   = 'Robots';
$_['entry_seo_url']                  = 'Use SEO URLs';
$_['entry_file_max_size']            = 'Max File Size';
$_['entry_file_ext_allowed']         = 'Allowed File Extensions';
$_['entry_file_mime_allowed']        = 'Allowed File Mime Types';
$_['entry_maintenance']              = 'Maintenance Mode';
$_['entry_password']                 = 'Allow Forgotten Password';
$_['entry_encryption']               = 'Encryption Key';
$_['entry_compression']              = 'Output Compression Level';
$_['entry_error_display']            = 'Display Errors';
$_['entry_error_log']                = 'Log Errors';
$_['entry_error_filename']           = 'Error Log Filename';
$_['entry_status']                   = 'Status';

// Help
$_['help_geocode']                   = 'Please enter your store location geocode manually.';
$_['help_open']                      = 'Fill in your store\'s opening times.';
$_['help_comment']                   = 'This field is for any special notes you would like to tell the customer i.e. Store does not accept cheques.';
$_['help_location']                  = 'The different store locations you have that you want displayed on the contact us form.';
$_['help_currency']                  = 'Change the default currency. Clear your browser cache to see the change and reset your existing cookie.';
$_['help_currency_auto']             = 'Set your store to automatically update currencies daily. It requires a chosen Currency Rate Engine.';
$_['help_limit_admin']               = 'Determines how many admin items are shown per page (orders, customers, etc).';
$_['help_product_count']             = 'Show the number of products inside the subcategories in the storefront header category menu. Be warned, this will cause an extreme performance hit for stores with a lot of subcategories!';
$_['help_review']                    = 'Enable/Disable new review entry and display of existing reviews.';
$_['help_review_guest']              = 'Allow guests to post reviews.';
$_['help_voucher_min']               = 'Minimum amount a customer can purchase a voucher for.';
$_['help_voucher_max']               = 'Maximum amount a customer can purchase a voucher for.';
$_['help_tax_default']               = 'Use the store address to calculate taxes if customer is not logged in. You can choose to use the store address for the customer\'s shipping or payment address.';
$_['help_tax_customer']              = 'Use the customer\'s default address when they login to calculate taxes. You can choose to use the default address for the customer\'s shipping or payment address.';
$_['help_customer_online']           = 'Track customers online via the customer reports section.';
$_['help_customer_activity']         = 'Track customers activity via the customer reports section.';
$_['help_customer_group']            = 'Default customer group.';
$_['help_customer_group_display']    = 'Display customer groups that new customers can select to use such as wholesale and business when signing up.';
$_['help_customer_price']            = 'Only show prices when a customer is logged in.';
$_['help_login_attempts']            = 'Maximum login attempts allowed before the account is locked for 1 hour. Customer and affliate accounts can be unlocked on the customer or affliate admin pages. Admin account can be unlocked on reset password.';
$_['help_account']                   = 'Forces people to agree to terms before an account can be created.';
$_['help_invoice_prefix']            = 'Set the invoice prefix (e.g. INV-2011-00). Invoice IDs will start at 1 for each unique prefix.';
$_['help_cart_weight']               = 'Show the cart weight on the cart page.';
$_['help_checkout_guest']            = 'Allow customers to checkout without creating an account. This will not be available when a downloadable product is in the shopping cart.';
$_['help_checkout']                  = 'Forces people to agree to terms before a customer can checkout.';
$_['help_order_status']              = 'Set the default order status when an order is processed.';
$_['help_processing_status']         = 'Set the order status the customer\'s order must reach before the order starts stock subtraction and coupon, voucher and rewards redemption.';
$_['help_complete_status']           = 'Set the order status the customer\'s order must reach before they are allowed to access their downloadable products and gift vouchers.';
$_['help_fraud_status']              = 'Set the order status when a customer is suspected of trying to alter the order payment details or use a coupon, gift voucher or reward points that have already been used.';
$_['help_api']                       = 'Default API user the admin should use.';
$_['help_stock_display']             = 'Display stock quantity on the product page.';
$_['help_stock_warning']             = 'Display out of stock message on the shopping cart page if a product is out of stock but stock checkout is yes. (Warning always shows if stock checkout is no)';
$_['help_stock_checkout']            = 'Allow customers to still checkout if the products they are ordering are not in stock.';
$_['help_affiliate_approval']        = 'Automatically approve any new affiliates who sign up.';
$_['help_affiliate_auto']            = 'Automatically add commission when each order reaches the complete status.';
$_['help_affiliate_commission']      = 'The default affiliate commission percentage.';
$_['help_affiliate']                 = 'Forces people to agree to terms before an affiliate account can be created.';
$_['help_return']                    = 'Forces people to agree to terms before a return can be created.';
$_['help_return_status']             = 'Set the default return status when a return request is submitted.';
$_['help_captcha']                   = 'Captcha to use for registration, login, contact and reviews.';
$_['help_icon']                      = 'The icon should be a PNG that is 16px x 16px.';
$_['help_mail_engine']               = 'Only choose \'Mail\' unless your host has disabled the php mail function.';
$_['help_mail_parameter']            = 'When using \'Mail\', additional mail parameters can be added here (e.g. -f <EMAIL>).';
$_['help_mail_smtp_hostname']        = 'Add \'tls://\' or \'ssl://\' prefix if security connection is required. (e.g. tls://smtp.gmail.com, ssl://smtp.gmail.com).';
$_['help_mail_smtp_password']        = 'For gmail you might need to setup a application specific password here: https://security.google.com/settings/security/apppasswords.';
$_['help_mail_alert']                = 'Select which features you would like to receive an alert email on when a customer uses them.';
$_['help_mail_alert_email']          = 'Any additional emails you want to receive the alert email, in addition to the main store email. (comma separated).';
$_['help_secure']                    = 'To use SSL check with your host if a SSL certificate is installed and add the SSL URL to the catalog and admin config files.';
$_['help_shared']                    = 'Try to share the session cookie between stores so the cart can be passed between different domains.';
$_['help_robots']                    = 'A list of web crawler user agents that shared sessions will not be used with. Use separate lines for each user agent.';
$_['help_seo_url']                   = 'To use SEO URLs, apache module mod-rewrite must be installed and you need to rename the htaccess.txt to .htaccess.';
$_['help_file_max_size']             = 'The maximum image file size you can upload in Image Manager. Enter as byte.';
$_['help_file_ext_allowed']          = 'Add which file extensions are allowed to be uploaded. Use a new line for each value.';
$_['help_file_mime_allowed']         = 'Add which file mime types are allowed to be uploaded. Use a new line for each value.';
$_['help_maintenance']               = 'Prevents customers from browsing your store. They will instead see a maintenance message. If logged in as admin, you will see the store as normal.';
$_['help_password']                  = 'Allow forgotten password to be used for the admin. This will be disabled automatically if the system detects a hack attempt.';
$_['help_encryption']                = 'Please provide a secret key that will be used to encrypt private information when processing orders.';
$_['help_compression']               = 'GZIP for more efficient transfer to requesting clients. Compression level must be between 0 - 9.';

// Error
$_['error_warning']                  = 'Warning: Please check the form carefully for errors!';
$_['error_permission']               = 'Warning: You do not have permission to modify settings!';
$_['error_meta_title']               = 'Title must be between 3 and 32 characters!';
$_['error_name']                     = 'Store Name must be between 3 and 32 characters!';
$_['error_owner']                    = 'Store Owner must be between 3 and 64 characters!';
$_['error_address']                  = 'Store Address must be between 10 and 256 characters!';
$_['error_email']                    = 'E-Mail Address does not appear to be valid!';
$_['error_telephone']                = 'Telephone must be between 3 and 32 characters!';
$_['error_limit']                    = 'Limit required!';
$_['error_login_attempts']           = 'Login Attempts must be greater than 0!';
$_['error_customer_group_display'] = 'You must include the default customer group if you are going to use this feature!';
$_['error_voucher_min']              = 'Minimum voucher amount required!';
$_['error_voucher_max']              = 'Maximum voucher amount required!';
$_['error_processing_status']        = 'You must choose at least 1 order process status';
$_['error_complete_status']          = 'You must choose at least 1 order complete status';
$_['error_log_required']             = 'Error Log Filename required!';
$_['error_log_invalid']              = 'Error Log Filename invalid!';
$_['error_log_extension']            = 'Error Log Filename extension needs to be .log!';
$_['error_encryption']               = 'Encryption Key must be between 32 and 1024 characters!';


$_['text_tab_accounting']        = 'Accounting Settings';
$_['text_inventory_valuation']   = 'Inventory Valuation Method';
$_['text_inventory_valuation_wac'] = 'Weighted Average (WAC)';
$_['text_assets_accounts']       = 'Assets Accounts';
$_['text_current_assets']        = 'Current Assets';
$_['text_cash_banks']            = 'Cash & Banks';
$_['text_petty_cash']            = 'Petty Cash';
$_['text_inventory_accounts']    = 'Inventory Accounts';
$_['text_main_inventory']        = 'Main Inventory';
$_['text_inventory_in_transit']  = 'Inventory in Transit';
$_['text_inventory_adjustment']  = 'Inventory Adjustment';
$_['text_receivables']           = 'Receivables';
$_['text_accounts_receivable']   = 'Accounts Receivable (AR)';
$_['text_liabilities_accounts']  = 'Liabilities Accounts';
$_['text_accounts_payable']      = 'Accounts Payable (AP)';
$_['text_loans']                 = 'Loans';
$_['text_purchase_accounts']     = 'Purchase Accounts';
$_['text_purchases']             = 'Purchases';
$_['text_purchase_returns']      = 'Purchase Returns';
$_['text_purchase_discounts']    = 'Purchase Discounts';
$_['text_sales_accounts']        = 'Sales Accounts';
$_['text_sales']                 = 'Sales';
$_['text_sales_returns']         = 'Sales Returns';
$_['text_sales_discounts']       = 'Sales Discounts';
$_['text_sales_shipping']        = 'Sales Shipping';
$_['text_tax_accounts']          = 'Tax Accounts';
$_['text_sales_tax']             = 'Sales Tax';
$_['text_purchase_tax']          = 'Purchase Tax';
$_['text_general_expenses']      = 'General Expenses';
$_['text_marketing_expenses']    = 'Marketing Expenses';
$_['text_salaries_expenses']     = 'Salaries & Wages';
$_['text_other_income']          = 'Other Income';

// Entry
$_['entry_inventory_valuation']  = 'Inventory Valuation';
$_['entry_cash_account']         = 'Cash Account';
$_['entry_petty_cash_account']   = 'Petty Cash Account';
$_['entry_bank_account']         = 'Bank Account';
$_['entry_ar_account']           = 'Accounts Receivable (AR)';
$_['entry_inventory_account']    = 'Main Inventory';
$_['entry_inventory_transit_account'] = 'Inventory in Transit';
$_['entry_inventory_adjustment_account'] = 'Inventory Adjustment';
$_['entry_ap_account']           = 'Accounts Payable (AP)';
$_['entry_loans_account']        = 'Loans Account';
$_['entry_purchase_account']     = 'Purchase Account';
$_['entry_purchase_returns_account'] = 'Purchase Returns';
$_['entry_purchase_discount_account'] = 'Purchase Discounts';
$_['entry_import_duties_account'] = 'Import Duties';
$_['entry_freight_charges_account'] = 'Freight Charges';
$_['entry_sales_account']        = 'Sales Account';
$_['entry_sales_returns_account'] = 'Sales Returns';
$_['entry_sales_discount_account'] = 'Sales Discounts';
$_['entry_sales_shipping_account'] = 'Sales Shipping';
$_['entry_sales_tax_account']    = 'Sales Tax';
$_['entry_purchase_tax_account'] = 'Purchase Tax';
$_['entry_general_expenses_account'] = 'General Expenses';
$_['entry_marketing_expenses_account'] = 'Marketing Expenses';
$_['entry_salaries_expenses_account'] = 'Salaries & Wages';
$_['entry_other_income_account'] = 'Other Income';

// Help
//$_['help_inventory_valuation']   = 'Choose the appropriate inventory valuation method';

// Advanced Financial Year Settings
$_['entry_financial_year_start']      = 'Financial Year Start';
$_['entry_financial_year_end']        = 'Financial Year End';
$_['entry_lock_date']                 = 'Lock Date';

// Advanced Currency Settings
$_['text_currency_settings']          = 'Currency Settings';
$_['entry_default_currency']          = 'Default Currency';
$_['entry_currency_rounding']         = 'Currency Rounding';
$_['text_round_nearest']              = 'Round to Nearest';
$_['text_round_down']                 = 'Round Down';
$_['entry_currency_precision']        = 'Currency Precision';

// Advanced Inventory Settings
$_['entry_inventory_valuation_method'] = 'Inventory Valuation Method';
$_['text_weighted_average']           = 'Weighted Average (WAC)';
$_['text_fifo']                       = 'First In First Out (FIFO)';
$_['text_lifo']                       = 'Last In First Out (LIFO)';
$_['text_standard_cost']              = 'Standard Cost';
$_['entry_auto_inventory_posting']    = 'Auto Inventory Posting';
$_['entry_allow_negative_inventory']  = 'Allow Negative Inventory';

// Auto Posting Settings
$_['entry_auto_sales_posting']        = 'Auto Sales Posting';
$_['entry_auto_purchase_posting']     = 'Auto Purchase Posting';

// Advanced ETA Settings
$_['text_eta_statistics']             = 'ETA Statistics';
$_['text_eta_connection_status']      = 'ETA Connection Status';
$_['text_eta_invoices_sent']          = 'Invoices Sent';
$_['text_eta_receipts_sent']          = 'Receipts Sent';
$_['text_eta_queue_count']            = 'Queue Count';
$_['text_eta_success_rate']           = 'Success Rate';
$_['text_eta_errors_count']           = 'Errors Count';
$_['text_eta_last_sync']              = 'Last Sync';
$_['text_eta_connected']              = 'Connected';
$_['text_eta_disconnected']           = 'Disconnected';
$_['text_eta_not_configured']         = 'Not Configured';

// Advanced Validation Messages
$_['error_financial_year_dates']      = 'Financial year end date must be after start date!';
$_['error_account_not_found']         = 'Selected account not found in chart of accounts!';
$_['error_eta_taxpayer_id']           = 'Invalid Egyptian taxpayer ID!';
$_['error_eta_required_fields']       = 'All ETA fields are required when enabled!';

// Additional Missing Variables from Audit Report - English Translations
$_['button_save']                      = 'Save';
$_['config_limit_admin']               = 'Admin Limit';
$_['config_lock_date']                 = 'Lock Date';
$_['config_login_attempts']            = 'Login Attempts';
$_['config_mail_alert_email']          = 'Alert Email';
$_['config_meta_keyword']              = 'Meta Keywords';
$_['config_region_city']               = 'Region/City';
$_['config_street']                    = 'Street';
$_['config_voucher_min']               = 'Voucher Minimum';
$_['config_zone_id']                   = 'Zone ID';
$_['currency_engine']                  = 'Currency Engine';
$_['error_limit_admin']                = 'Admin limit is required!';
$_['error_log']                        = 'Error Log';
$_['extension']                        = 'Extension';
$_['extension/captcha/']               = 'Extension/Captcha/';
$_['extension/currency/']              = 'Extension/Currency/';
$_['extension/theme/']                 = 'Extension/Theme/';
$_['icon']                             = 'Icon';
$_['logo']                             = 'Logo';
$_['placeholder']                      = 'Placeholder';
$_['success']                          = 'Success';
$_['tab_general']                      = 'General';
$_['text_home']                        = 'Home';
$_['text_upload']                      = 'Upload';
$_['text_weighted_average']            = 'Weighted Average';
$_['thumb']                            = 'Thumbnail';
$_['user_token']                       = 'User Token';

// Error
//$_['error_accounting_permission'] = 'Warning: You do not have permission to modify accounting settings!';
// Add other errors as needed
?>
