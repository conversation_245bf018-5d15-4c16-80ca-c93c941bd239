<?php
/**
 * AYM ERP Settings Model - Enterprise Grade Plus
 * Enhanced with SQL injection prevention and security improvements
 *
 * <AUTHOR> ERP Development Team
 * @version 2.0 Enterprise Grade Plus
 * @since 2025-01-22
 */
class ModelSettingSetting extends Model {

	/**
	 * Get settings for a specific code with enhanced security
	 *
	 * @param string $code Setting code
	 * @param int $store_id Store ID (default: 0)
	 * @return array Settings data
	 */
	public function getSetting($code, $store_id = 0) {
		$setting_data = array();

		// Enhanced input validation
		if (empty($code) || !is_string($code)) {
			return $setting_data;
		}

		$store_id = (int)$store_id;

		try {
			// Use prepared statement to prevent SQL injection
			$sql = "SELECT `key`, `value`, `serialized` FROM `" . DB_PREFIX . "setting` WHERE `store_id` = ? AND `code` = ?";
			$query = $this->db->query($sql, array($store_id, $code));

			foreach ($query->rows as $result) {
				if (!$result['serialized']) {
					$setting_data[$result['key']] = $result['value'];
				} else {
					$decoded_value = json_decode($result['value'], true);
					$setting_data[$result['key']] = ($decoded_value !== null) ? $decoded_value : $result['value'];
				}
			}
		} catch (Exception $e) {
			// Log error for debugging
			error_log('Settings Model getSetting Error: ' . $e->getMessage());
		}

		return $setting_data;
	}

	/**
	 * Edit settings with enhanced security and transaction support
	 *
	 * @param string $code Setting code
	 * @param array $data Settings data
	 * @param int $store_id Store ID (default: 0)
	 * @return bool Success status
	 */
	public function editSetting($code, $data, $store_id = 0) {
		// Enhanced input validation
		if (empty($code) || !is_string($code) || !is_array($data)) {
			return false;
		}

		$store_id = (int)$store_id;

		try {
			// Start transaction for data integrity
			$this->db->query("START TRANSACTION");

			// Delete existing settings using prepared statement
			$delete_sql = "DELETE FROM `" . DB_PREFIX . "setting` WHERE `store_id` = ? AND `code` = ?";
			$this->db->query($delete_sql, array($store_id, $code));

			// Insert new settings
			$insert_sql = "INSERT INTO `" . DB_PREFIX . "setting` SET `store_id` = ?, `code` = ?, `key` = ?, `value` = ?, `serialized` = ?";

			foreach ($data as $key => $value) {
				if (substr($key, 0, strlen($code)) == $code) {
					if (!is_array($value)) {
						$this->db->query($insert_sql, array($store_id, $code, $key, $value, 0));
					} else {
						$json_value = json_encode($value, JSON_UNESCAPED_UNICODE);
						$this->db->query($insert_sql, array($store_id, $code, $key, $json_value, 1));
					}
				}
			}

			// Commit transaction
			$this->db->query("COMMIT");
			return true;

		} catch (Exception $e) {
			// Rollback on error
			$this->db->query("ROLLBACK");
			error_log('Settings Model editSetting Error: ' . $e->getMessage());
			return false;
		}
	}

	/**
	 * Delete settings with enhanced security
	 *
	 * @param string $code Setting code
	 * @param int $store_id Store ID (default: 0)
	 * @return bool Success status
	 */
	public function deleteSetting($code, $store_id = 0) {
		// Enhanced input validation
		if (empty($code) || !is_string($code)) {
			return false;
		}

		$store_id = (int)$store_id;

		try {
			$sql = "DELETE FROM `" . DB_PREFIX . "setting` WHERE `store_id` = ? AND `code` = ?";
			$this->db->query($sql, array($store_id, $code));
			return true;
		} catch (Exception $e) {
			error_log('Settings Model deleteSetting Error: ' . $e->getMessage());
			return false;
		}
	}

	/**
	 * Get single setting value with enhanced security
	 *
	 * @param string $key Setting key
	 * @param int $store_id Store ID (default: 0)
	 * @return mixed Setting value or null
	 */
	public function getSettingValue($key, $store_id = 0) {
		// Enhanced input validation
		if (empty($key) || !is_string($key)) {
			return null;
		}

		$store_id = (int)$store_id;

		try {
			$sql = "SELECT `value`, `serialized` FROM `" . DB_PREFIX . "setting` WHERE `store_id` = ? AND `key` = ?";
			$query = $this->db->query($sql, array($store_id, $key));

			if ($query->num_rows) {
				if (!$query->row['serialized']) {
					return $query->row['value'];
				} else {
					$decoded_value = json_decode($query->row['value'], true);
					return ($decoded_value !== null) ? $decoded_value : $query->row['value'];
				}
			}
		} catch (Exception $e) {
			error_log('Settings Model getSettingValue Error: ' . $e->getMessage());
		}

		return null;
	}

	/**
	 * Edit single setting value with enhanced security
	 *
	 * @param string $code Setting code
	 * @param string $key Setting key
	 * @param mixed $value Setting value
	 * @param int $store_id Store ID (default: 0)
	 * @return bool Success status
	 */
	public function editSettingValue($code = '', $key = '', $value = '', $store_id = 0) {
		// Enhanced input validation
		if (empty($code) || empty($key) || !is_string($code) || !is_string($key)) {
			return false;
		}

		$store_id = (int)$store_id;

		try {
			if (!is_array($value)) {
				$sql = "UPDATE `" . DB_PREFIX . "setting` SET `value` = ?, `serialized` = 0 WHERE `code` = ? AND `key` = ? AND `store_id` = ?";
				$this->db->query($sql, array($value, $code, $key, $store_id));
			} else {
				$json_value = json_encode($value, JSON_UNESCAPED_UNICODE);
				$sql = "UPDATE `" . DB_PREFIX . "setting` SET `value` = ?, `serialized` = 1 WHERE `code` = ? AND `key` = ? AND `store_id` = ?";
				$this->db->query($sql, array($json_value, $code, $key, $store_id));
			}
			return true;
		} catch (Exception $e) {
			error_log('Settings Model editSettingValue Error: ' . $e->getMessage());
			return false;
		}
	}
}
